# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (keep local ones)
.env.local
.env.development.local
.env.test.local

# Build outputs and unused directories
backend/
mobile/
docs/
scripts/
prisma/
.github/

# Keep client-portal and admin-portal for Vercel builds
# client-portal/ - REMOVED (needed for build)
# admin-portal/ - REMOVED (needed for build)

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov

# Logs
logs
*.log

# Temporary folders
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
