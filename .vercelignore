# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables (keep local ones)
.env.local
.env.development.local
.env.test.local

# Build outputs (except the one we want)
backend/
mobile/
client-portal/
docs/
scripts/
prisma/
.github/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov

# Logs
logs
*.log

# Temporary folders
tmp/
temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
