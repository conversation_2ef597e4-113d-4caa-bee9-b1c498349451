{
  "env": {
    "node": true,
    "es2022": true,
    "jest": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:node/recommended",
    "plugin:security/recommended",
    "plugin:prettier/recommended"
  ],
  "plugins": [
    "node",
    "security",
    "prettier"
  ],
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module"
  },
  "rules": {
    // Production-ready code standards
    "no-console": "warn",
    "no-debugger": "error",
    "no-alert": "error",
    "no-eval": "error",
    "no-implied-eval": "error",
    "no-new-func": "error",
    "no-script-url": "error",
    "no-var": "error",
    "prefer-const": "error",
    "prefer-arrow-callback": "error",
    "arrow-spacing": "error",
    "no-duplicate-imports": "error",
    "no-useless-rename": "error",
    "object-shorthand": "error",
    "prefer-template": "error",
    "template-curly-spacing": "error",

    // Error handling and validation
    "no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }
    ],
    "no-unreachable": "error",
    "no-unsafe-finally": "error",
    "no-unsafe-negation": "error",
    "valid-typeof": "error",
    "no-constant-condition": [
      "error",
      {
        "checkLoops": false
      }
    ],

    // Security rules
    "security/detect-object-injection": "warn",
    "security/detect-non-literal-regexp": "warn",
    "security/detect-unsafe-regex": "error",
    "security/detect-buffer-noassert": "error",
    "security/detect-child-process": "warn",
    "security/detect-disable-mustache-escape": "error",
    "security/detect-eval-with-expression": "error",
    "security/detect-no-csrf-before-method-override": "error",
    "security/detect-non-literal-fs-filename": "warn",
    "security/detect-non-literal-require": "warn",
    "security/detect-possible-timing-attacks": "warn",
    "security/detect-pseudoRandomBytes": "error",

    // Node.js specific rules
    "node/no-unsupported-features/es-syntax": "off",
    "node/no-missing-import": "off",
    "node/no-unpublished-import": "off",
    "node/no-extraneous-import": "off",
    "node/no-missing-require": "off",
    "node/no-unpublished-require": "off",
    "node/no-extraneous-require": "off",
    "node/no-process-exit": "error",
    "node/no-callback-literal": "error",
    "node/no-new-require": "error",
    "node/no-path-concat": "error",
    "node/no-sync": "warn",
    "node/prefer-promises/fs": "warn",
    "node/prefer-promises/dns": "warn",
    "node/prefer-promises/child-process": "warn",

    // Code quality and maintainability
    "complexity": [
      "warn",
      10
    ],
    "max-depth": [
      "warn",
      4
    ],
    "max-lines": [
      "warn",
      300
    ],
    "max-lines-per-function": [
      "warn",
      50
    ],
    "max-params": [
      "warn",
      5
    ],
    "max-statements": [
      "warn",
      20
    ],
    "no-magic-numbers": [
      "warn",
      {
        "ignore": [
          -1,
          0,
          1,
          2,
          100,
          200,
          300,
          400,
          401,
          403,
          404,
          500
        ],
        "ignoreArrayIndexes": true,
        "detectObjects": false
      }
    ],
    "prefer-destructuring": [
      "error",
      {
        "array": true,
        "object": true
      },
      {
        "enforceForRenamedProperties": false
      }
    ],

    // Async/await and promises
    "no-async-promise-executor": "error",
    "no-misused-promises": "error",
    "no-promise-executor-return": "error",
    "require-atomic-updates": "error",
    "prefer-promise-reject-errors": "error",

    // Code style and formatting
    "indent": [
      "error",
      2,
      {
        "SwitchCase": 1
      }
    ],
    "linebreak-style": [
      "error",
      "unix"
    ],
    "quotes": [
      "error",
      "single",
      {
        "avoidEscape": true
      }
    ],
    "semi": [
      "error",
      "always"
    ],
    "comma-dangle": [
      "error",
      "never"
    ],
    "no-trailing-spaces": "error",
    "eol-last": "error",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 2,
        "maxEOF": 1
      }
    ],
    "padded-blocks": [
      "error",
      "never"
    ],
    "space-before-blocks": "error",
    "keyword-spacing": "error",
    "space-infix-ops": "error",
    "space-before-function-paren": [
      "error",
      {
        "anonymous": "always",
        "named": "never",
        "asyncArrow": "always"
      }
    ],
    "func-call-spacing": "error",
    "no-spaced-func": "error",
    "object-curly-spacing": [
      "error",
      "always"
    ],
    "array-bracket-spacing": [
      "error",
      "never"
    ],
    "computed-property-spacing": [
      "error",
      "never"
    ],
    "comma-spacing": "error",
    "comma-style": "error",
    "key-spacing": "error",
    "brace-style": [
      "error",
      "1tbs",
      {
        "allowSingleLine": true
      }
    ],
    "camelcase": [
      "error",
      {
        "properties": "never"
      }
    ],
    "new-cap": "error",
    "new-parens": "error",
    "no-array-constructor": "error",
    "no-new-object": "error",
    "no-new-wrappers": "error",
    "no-underscore-dangle": [
      "error",
      {
        "allow": [
          "_id",
          "_v",
          "__dirname",
          "__filename"
        ],
        "allowAfterThis": false,
        "allowAfterSuper": false
      }
    ],

    // Best practices for production code
    "no-else-return": "error",
    "no-empty-function": [
      "error",
      {
        "allow": [
          "arrowFunctions"
        ]
      }
    ],
    "no-implicit-coercion": "error",
    "no-implicit-globals": "error",
    "no-invalid-this": "error",
    "no-iterator": "error",
    "no-labels": "error",
    "no-lone-blocks": "error",
    "no-loop-func": "error",
    "no-multi-spaces": "error",
    "no-multi-str": "error",
    "no-new": "error",
    "no-octal": "error",
    "no-octal-escape": "error",
    "no-proto": "error",
    "no-return-assign": "error",
    "no-return-await": "error",
    "no-self-assign": "error",
    "no-self-compare": "error",
    "no-sequences": "error",
    "no-throw-literal": "error",
    "no-unmodified-loop-condition": "error",
    "no-unused-expressions": "error",
    "no-useless-call": "error",
    "no-useless-concat": "error",
    "no-useless-escape": "error",
    "no-useless-return": "error",
    "no-void": "error",
    "no-warning-comments": [
      "warn",
      {
        "terms": [
          "todo",
          "fixme",
          "xxx"
        ],
        "location": "start"
      }
    ],
    "prefer-numeric-literals": "error",
    "radix": "error",
    "require-await": "error",
    "yoda": "error",

    // Prettier integration
    "prettier/prettier": "error"
  },
  "overrides": [
    {
      "files": [
        "**/*.test.js",
        "**/*.spec.js",
        "tests/**/*.js"
      ],
      "env": {
        "jest": true
      },
      "rules": {
        "no-console": "off",
        "max-lines": "off",
        "max-lines-per-function": "off",
        "complexity": "off"
      }
    },
    {
      "files": [
        "scripts/**/*.js"
      ],
      "rules": {
        "no-console": "off",
        "security/detect-child-process": "off"
      }
    },
    {
      "files": [
        "**/migrations/**/*.js"
      ],
      "rules": {
        "no-console": "off"
      }
    }
  ],
  "settings": {
    "node": {
      "tryExtensions": [
        ".js",
        ".json",
        ".node"
      ]
    }
  },
  "ignorePatterns": [
    "node_modules/",
    "dist/",
    "coverage/",
    "*.min.js",
    "prisma/generated/"
  ]
} 