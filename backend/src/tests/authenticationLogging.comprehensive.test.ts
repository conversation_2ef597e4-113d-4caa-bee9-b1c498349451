import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { Request } from 'express';
import { AuthenticationService } from '../middleware/auth';
import { TokenType } from '../types/auth';
import { logger } from '../utils/logger';

// Store original logger methods to restore later
const originalLogger = {
  debug: logger.debug,
  info: logger.info,
  warn: logger.warn,
  error: logger.error
};

// Create arrays to capture log calls without mocking
const logCapture = {
  debug: [] as any[],
  info: [] as any[],
  warn: [] as any[],
  error: [] as any[]
};

// Override logger methods to capture calls
const captureLogger = {
  debug: (...args: any[]) => {
    logCapture.debug.push(args);
    // Don't call original logger to avoid Winston issues
    return logger; // Return logger instance to match LeveledLogMethod signature
  },
  info: (...args: any[]) => {
    logCapture.info.push(args);
    // Don't call original logger to avoid Winston issues
    return logger; // Return logger instance to match LeveledLogMethod signature
  },
  warn: (...args: any[]) => {
    logCapture.warn.push(args);
    // Don't call original logger to avoid Winston issues
    return logger; // Return logger instance to match LeveledLogMethod signature
  },
  error: (...args: any[]) => {
    logCapture.error.push(args);
    // Don't call original logger to avoid Winston issues
    return logger; // Return logger instance to match LeveledLogMethod signature
  }
};

describe('Authentication Logging Comprehensive Tests (No Mock Data)', () => {
  let authService: AuthenticationService;
  let originalNodeEnv: string | undefined;

  beforeEach(() => {
    originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'test';
    process.env.CLERK_SECRET_KEY = 'sk_test_valid_clerk_secret_key_12345678901234567890';
    
    authService = AuthenticationService.getInstance();
    
    // Clear log capture arrays
    logCapture.debug.length = 0;
    logCapture.info.length = 0;
    logCapture.warn.length = 0;
    logCapture.error.length = 0;
    
    // Replace logger methods with capturing versions
    logger.debug = captureLogger.debug;
    logger.info = captureLogger.info;
    logger.warn = captureLogger.warn;
    logger.error = captureLogger.error;
  });

  afterEach(() => {
    // Restore original logger methods
    logger.debug = originalLogger.debug;
    logger.info = originalLogger.info;
    logger.warn = originalLogger.warn;
    logger.error = originalLogger.error;
    
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv as any;
    }
  });

  describe('Token Type Detection Logging', () => {
    it('should log token type detection with appropriate level', async () => {
      const testEmail = `logging-email-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Should log token type detection at debug level
        const debugLogs = logCapture.debug.filter(log => 
          log[0] === 'Token type detected'
        );
        
        expect(debugLogs.length).toBeGreaterThan(0);
        const tokenDetectionLog = debugLogs[0];
        expect(tokenDetectionLog[1]).toMatchObject({
          tokenType: TokenType.EMAIL,
          tokenLength: testEmail.length,
          tokenPrefix: expect.stringContaining(testEmail.substring(0, 10))
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping logging test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should log development token detection with environment context', async () => {
      const testEmail = `logging-dev-${Date.now()}@example.com`;
      const devToken = `dev:${testEmail}`;
      const mockRequest = {
        headers: { authorization: `Bearer ${devToken}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Should log token type detection
        const debugLogs = logCapture.debug.filter(log => 
          log[0] === 'Token type detected'
        );
        
        expect(debugLogs.length).toBeGreaterThan(0);
        const tokenDetectionLog = debugLogs[0];
        expect(tokenDetectionLog[1]).toMatchObject({
          tokenType: TokenType.DEVELOPMENT,
          tokenLength: devToken.length
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping dev logging test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should log JWT token detection attempts', async () => {
      const jwtToken = 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjMifQ.test';
      const mockRequest = {
        headers: { authorization: `Bearer ${jwtToken}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Expected to fail, but should log token detection
        const debugLogs = logCapture.debug.filter(log => 
          log[0] === 'Token type detected'
        );
        
        expect(debugLogs.length).toBeGreaterThan(0);
        const tokenDetectionLog = debugLogs[0];
        expect(tokenDetectionLog[1]).toMatchObject({
          tokenType: TokenType.JWT,
          tokenLength: jwtToken.length
        });
      }
    });
  });

  describe('Authentication Success Logging', () => {
    it('should log successful email authentication with comprehensive details', async () => {
      const testEmail = `success-email-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        const result = await authService.authenticateRequest(mockRequest);
        
        // Should log successful authentication at info level
        const infoLogs = logCapture.info.filter(log => 
          log[0] === 'Authentication successful'
        );
        
        expect(infoLogs.length).toBeGreaterThan(0);
        const successLog = infoLogs[0];
        expect(successLog[1]).toMatchObject({
          userId: result.user.id,
          email: testEmail,
          role: 'ADMIN',
          tokenType: TokenType.EMAIL,
          authenticationMethod: 'email'
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping success logging test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should log successful development authentication with environment info', async () => {
      const testEmail = `success-dev-${Date.now()}@example.com`;
      const devToken = `dev:${testEmail}`;
      const mockRequest = {
        headers: { authorization: `Bearer ${devToken}` }
      } as Request;

      try {
        const result = await authService.authenticateRequest(mockRequest);
        
        // Should log successful authentication
        const infoLogs = logCapture.info.filter(log => 
          log[0] === 'Authentication successful'
        );
        
        expect(infoLogs.length).toBeGreaterThan(0);
        const successLog = infoLogs[0];
        expect(successLog[1]).toMatchObject({
          userId: result.user.id,
          email: testEmail,
          role: 'ADMIN',
          tokenType: TokenType.DEVELOPMENT,
          authenticationMethod: 'development'
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping dev success logging test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should not log sensitive token information in success logs', async () => {
      const testEmail = `sensitive-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Check that full token is not logged in success messages
        const successLogs = logCapture.info.filter(log => 
          log[0] === 'Authentication successful'
        );
        
        expect(successLogs.length).toBeGreaterThan(0);
        successLogs.forEach(log => {
          const logData = log[1] as any;
          expect(logData).not.toHaveProperty('token');
          // Email is expected to be in success logs for audit purposes
          // but should not contain the full token
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping sensitive logging test - database not available');
          return;
        }
        throw error;
      }
    });
  });

  describe('Authentication Failure Logging', () => {
    it('should log authentication failures with appropriate error details', async () => {
      const invalidEmail = 'invalid-email-format';
      const mockRequest = {
        headers: { authorization: `Bearer ${invalidEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Should log authentication failure at error level
        const errorLogs = logCapture.error.filter(log => 
          log[0] === 'Authentication failed'
        );
        
        expect(errorLogs.length).toBeGreaterThan(0);
        const failureLog = errorLogs[0];
        expect(failureLog[1]).toMatchObject({
          error: expect.stringMatching(/(Email authentication failed|JWT authentication failed)/),
          tokenLength: invalidEmail.length,
          tokenPrefix: expect.stringContaining(invalidEmail.substring(0, 10))
        });
      }
    });

    it('should log JWT authentication failures without exposing token content', async () => {
      const invalidJWT = 'eyJhbGciOiJIUzI1NiJ9.invalid.token';
      const mockRequest = {
        headers: { authorization: `Bearer ${invalidJWT}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Should log failure with masked token info
        const errorLogs = logCapture.error.filter(log => 
          log[0] === 'Authentication failed'
        );
        
        expect(errorLogs.length).toBeGreaterThan(0);
        const failureLog = errorLogs[0];
        expect(failureLog[1]).toMatchObject({
          error: expect.stringContaining('JWT authentication failed'),
          tokenLength: invalidJWT.length,
          tokenPrefix: expect.stringContaining('eyJhbGciOi')
        });
        
        // Should not log the full token
        const allErrorLogs = logCapture.error;
        allErrorLogs.forEach(log => {
          expect(JSON.stringify(log)).not.toContain(invalidJWT);
        });
      }
    });

    it('should log development authentication failures with environment context', async () => {
      const invalidDevToken = 'dev:invalid-email-format';
      const mockRequest = {
        headers: { authorization: `Bearer ${invalidDevToken}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Should log development authentication failure
        const errorLogs = logCapture.error.filter(log => 
          log[0] === 'Authentication failed'
        );
        
        expect(errorLogs.length).toBeGreaterThan(0);
        const failureLog = errorLogs[0];
        expect(failureLog[1]).toMatchObject({
          error: expect.stringContaining('Development authentication failed'),
          tokenLength: invalidDevToken.length
        });
      }
    });

    it('should handle missing authorization header logging', async () => {
      const mockRequest = {
        headers: {}
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Should not log detailed error for missing token (security)
        // But should handle gracefully without exposing system internals
        expect(error).toBeDefined();
      }
    });
  });

  describe('Log Level Appropriateness', () => {
    it('should use appropriate log levels for different scenarios', async () => {
      const testEmail = `log-levels-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Debug level for token detection
        const debugLogs = logCapture.debug.filter(log => 
          log[0] === 'Token type detected'
        );
        expect(debugLogs.length).toBeGreaterThan(0);
        
        // Info level for successful authentication
        const infoLogs = logCapture.info.filter(log => 
          log[0] === 'Authentication successful'
        );
        expect(infoLogs.length).toBeGreaterThan(0);
        
        // Should not use warn or error for successful authentication
        const warnLogs = logCapture.warn;
        const errorLogs = logCapture.error;
        
        expect(warnLogs.length).toBe(0);
        expect(errorLogs.length).toBe(0);
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping log level test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should not log warnings for email tokens (no JWT validation attempts)', async () => {
      const testEmail = `no-warnings-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Should not have any warning logs for email authentication
        const warnLogs = logCapture.warn;
        const jwtWarnings = warnLogs.filter(log => 
          JSON.stringify(log).includes('JWT') || 
          JSON.stringify(log).includes('jwt')
        );
        
        expect(jwtWarnings.length).toBe(0);
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping warning test - database not available');
          return;
        }
        throw error;
      }
    });
  });

  describe('Security and Privacy in Logging', () => {
    it('should not log sensitive information in any log level', async () => {
      const sensitiveEmail = `sensitive-data-${Date.now()}@secret-company.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${sensitiveEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Check all log calls for sensitive information
        const allLogs = [
          ...logCapture.debug,
          ...logCapture.info,
          ...logCapture.warn,
          ...logCapture.error
        ];
        
        allLogs.forEach(log => {
          const logString = JSON.stringify(log);
          // Should not log full email in most contexts except for legitimate audit logs
          if (log[0] !== 'Authentication successful' && 
              log[0] !== 'Creating new user for email authentication' &&
              log[0] !== 'Email authentication successful') {
            expect(logString).not.toContain(sensitiveEmail);
          }
        });
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping security logging test - database not available');
          return;
        }
        throw error;
      }
    });

    it('should mask or truncate tokens in error logs', async () => {
      const longToken = 'eyJhbGciOiJIUzI1NiJ9.' + 'a'.repeat(100) + '.' + 'b'.repeat(100);
      const mockRequest = {
        headers: { authorization: `Bearer ${longToken}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Check that full token is not logged
        const errorLogs = logCapture.error;
        errorLogs.forEach(log => {
          const logString = JSON.stringify(log);
          expect(logString).not.toContain(longToken);
          
          // Should contain truncated version
          if (logString.includes('tokenPrefix')) {
            expect(logString).toContain('eyJhbGciOi');
          }
        });
      }
    });

    it('should handle logging of special characters safely', async () => {
      const specialEmail = `test+special.chars-${Date.now()}@domain.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${specialEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Should handle special characters without breaking logs
        const allLogs = [
          ...logCapture.debug,
          ...logCapture.info,
          ...logCapture.warn,
          ...logCapture.error
        ];
        
        // All calls should be valid (no thrown errors during logging)
        expect(allLogs.length).toBeGreaterThan(0);
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping special chars logging test - database not available');
          return;
        }
        throw error;
      }
    });
  });

  describe('Performance and Volume Considerations', () => {
    it('should not generate excessive log entries for single authentication', async () => {
      const testEmail = `performance-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      // Clear previous calls
      logCapture.debug.length = 0;
      logCapture.info.length = 0;
      logCapture.warn.length = 0;
      logCapture.error.length = 0;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Should have reasonable number of log entries
        const totalLogs = logCapture.debug.length +
                         logCapture.info.length +
                         logCapture.warn.length +
                         logCapture.error.length;
        
        // Should not exceed reasonable limit (adjust as needed)
        expect(totalLogs).toBeLessThan(10);
        
        // Should have at least token detection and success logs
        expect(totalLogs).toBeGreaterThanOrEqual(2);
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping performance logging test - database not available');
          return;
        }
        throw error;
      }
    });
  });

  describe('Log Message Clarity and Usefulness', () => {
    it('should provide clear and actionable error messages', async () => {
      const invalidToken = 'dev:invalid-email-format';
      const mockRequest = {
        headers: { authorization: `Bearer ${invalidToken}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
      } catch (error) {
        // Error logs should be clear and actionable
        const errorLogs = logCapture.error.filter(log => 
          log[0] === 'Authentication failed'
        );
        
        expect(errorLogs.length).toBeGreaterThan(0);
        const authErrorLog = errorLogs[0];
        const errorData = authErrorLog[1] as any;
        expect(errorData.error).toContain('Invalid email format');
        expect(errorData.tokenLength).toBeDefined();
      }
    });

    it('should provide sufficient context for debugging', async () => {
      const testEmail = `debug-context-${Date.now()}@example.com`;
      const mockRequest = {
        headers: { authorization: `Bearer ${testEmail}` }
      } as Request;

      try {
        await authService.authenticateRequest(mockRequest);
        
        // Debug logs should provide sufficient context
        const debugLogs = logCapture.debug.filter(log => 
          log[0] === 'Token type detected'
        );
        
        expect(debugLogs.length).toBeGreaterThan(0);
        const tokenDetectionLog = debugLogs[0];
        const debugData = tokenDetectionLog[1] as any;
        expect(debugData).toHaveProperty('tokenType');
        expect(debugData).toHaveProperty('tokenLength');
        expect(debugData).toHaveProperty('tokenPrefix');
      } catch (error) {
        if (error instanceof Error && error.message.includes('database')) {
          console.warn('Skipping debug context test - database not available');
          return;
        }
        throw error;
      }
    });
  });
});