{"version": 3, "file": "reportGenerationService.js", "sourceRoot": "", "sources": ["../../src/services/reportGenerationService.ts"], "names": [], "mappings": ";;;;;;AAAA,2CAA8C;AAC9C,oDAAiC;AACjC,sDAA8B;AAC9B,4CAAoB;AACpB,gDAAwB;AAExB,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAgBlC,MAAa,uBAAuB;IAIlC;QACE,IAAI,CAAC,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,YAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,CAAC;YACtC,uBAAuB,CAAC,QAAQ,GAAG,IAAI,uBAAuB,EAAE,CAAC;QACnE,CAAC;QACD,OAAO,uBAAuB,CAAC,QAAQ,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,OAAgC;QAC3D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEhE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAA4C;QAC1E,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;gBACtB,IAAI,OAAO,CAAC,SAAS;oBAAE,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;gBAChE,IAAI,OAAO,CAAC,OAAO;oBAAE,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC;YAC9D,CAAC;YACD,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,OAAO,CAAC,QAAQ;gBAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACxD,IAAI,OAAO,CAAC,MAAM;gBAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAClD,IAAI,OAAO,CAAC,IAAI;gBAAE,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9C,CAAC;QAED,OAAO,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,KAAK;YACL,OAAO,EAAE;gBACP,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,IAAI;wBACb,MAAM,EAAE;4BACN,MAAM,EAAE;gCACN,WAAW,EAAE,IAAI;6BAClB;yBACF;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,KAAK,EAAE,IAAI;wBACX,MAAM,EAAE,IAAI;wBACZ,SAAS,EAAE,IAAI;qBAChB;iBACF;aACF;YACD,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAChC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAAgB,EAAE,OAAgC;QAChF,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;QACrD,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEtD,MAAM,GAAG,GAAG,IAAI,gBAAW,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5C,GAAG,CAAC,IAAI,CAAC,YAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAGzC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC9D,GAAG,CAAC,QAAQ,EAAE,CAAC;QAGf,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACjB,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACtD,GAAG,CAAC,IAAI,CAAC,oBAAoB,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QACjD,GAAG,CAAC,QAAQ,EAAE,CAAC;QAGf,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACrD,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACjB,GAAG,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,mBAAmB,KAAK,CAAC,UAAU,gBAAgB,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QACjG,GAAG,CAAC,IAAI,CAAC,aAAa,KAAK,CAAC,QAAQ,YAAY,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5G,GAAG,CAAC,QAAQ,EAAE,CAAC;QAGf,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/D,GAAG,CAAC,QAAQ,EAAE,CAAC;QAEf,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,GAAG,CAAC;gBAAE,GAAG,CAAC,OAAO,EAAE,CAAC;YAE7B,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACrE,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACjB,GAAG,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/B,GAAG,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;YACrC,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACnC,GAAG,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3C,GAAG,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YACvC,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC;YACtD,GAAG,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,IAAI,SAAS,EAAE,CAAC,CAAC;YACvE,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YAExE,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,GAAG,CAAC,IAAI,CAAC,gBAAgB,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtG,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,GAAG,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC7C,CAAC;YAED,GAAG,CAAC,QAAQ,EAAE,CAAC;YACf,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/C,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACxB,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACf,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;gBACxE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC1I,GAAG,CAAC,IAAI,CAAC,oBAAoB,cAAc,QAAQ,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACf,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAW,EAAE,EAAE;oBACvC,GAAG,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBACnD,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,GAAG,EAAE,CAAC;QAEV,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAAgB,EAAE,OAAgC;QAClF,MAAM,QAAQ,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC;QACtD,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAEtD,MAAM,QAAQ,GAAG,IAAI,iBAAO,CAAC,QAAQ,EAAE,CAAC;QAGxC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACtD,YAAY,CAAC,OAAO,GAAG;YACrB,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9C,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;SAC7C,CAAC;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QACrD,YAAY,CAAC,OAAO,CAAC;YACnB,EAAE,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE;YACtD,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;YACrC,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE;YAClD,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;YAC7C,EAAE,MAAM,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;YACtD,EAAE,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;YAC9C,EAAE,MAAM,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE;YAClD,EAAE,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE;SAC7C,CAAC,CAAC;QAGH,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC1D,cAAc,CAAC,OAAO,GAAG;YACvB,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;YAC5C,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE;YAC1C,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;YAClD,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9C,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE;YAC1C,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE;YAC9C,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE;YACvD,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE;YACvD,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE;YACvD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;YAClD,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE;SACzD,CAAC;QAEF,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,cAAc,CAAC,MAAM,CAAC;gBACpB,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS;gBACtC,MAAM,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,IAAI,SAAS;gBACvD,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;oBAC/B,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC9E,QAAQ;gBACV,UAAU,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,cAAc,EAAE;gBAC1D,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE;gBACrF,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;gBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAGH,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC7C,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YACtC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG;gBACrB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;aAC9B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,sBAAsB,CAAC,SAAgB;QAC7C,OAAO;YACL,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;YACvD,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,CAAC,MAAM;YACpE,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,MAAM;YAC/D,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM;YACjE,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;YACzD,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;YAC7D,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;SACxD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACtD,IAAI,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,cAAsB,EAAE;QAC9C,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAE5C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE,CAAC;gBACzC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAzQD,0DAyQC;AAED,kBAAe,uBAAuB,CAAC"}