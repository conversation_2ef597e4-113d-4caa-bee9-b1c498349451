{"version": 3, "file": "locationService.js", "sourceRoot": "", "sources": ["../../src/services/locationService.ts"], "names": [], "mappings": ";;AAAA,2CAA8C;AAC9C,4CAAyC;AACzC,2CAA8C;AA6B9C,MAAM,eAAe;IAKnB;QAHiB,uBAAkB,GAAG,GAAG,CAAC;QACzB,oCAA+B,GAAG,EAAE,CAAC;QAGpD,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,cAA8B;QACxD,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAG/C,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAGhD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;YAGtE,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO,eAAe,CAAC;YACzB,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACvD,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;qBAC/C;iBACF;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE;wBACL,OAAO,EAAE;4BACP,IAAI,EAAE;gCACJ,MAAM,EAAE;oCACN,SAAS,EAAE,IAAI;oCACf,QAAQ,EAAE,IAAI;oCACd,KAAK,EAAE,IAAI;iCACZ;6BACF;yBACF;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACtB,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACtD,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC7E,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,YAAY,EAAE,QAAQ,CAAC,OAAO;aAC/B,CAAC,CAAC,CAAC;YAGJ,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAEhD,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,SAAe,EAAE,OAAa;QACtE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACvD,KAAK,EAAE;oBACL,OAAO;oBACP,SAAS,EAAE;wBACT,GAAG,EAAE,SAAS;wBACd,GAAG,EAAE,OAAO;qBACb;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAChC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,YAAY,EAAE,QAAQ,CAAC,OAAO;aAC/B,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAEhF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC5D,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,EAAE,EAAE,UAAU;qBACf;iBACF;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,CAAC,KAAK,uBAAuB,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,mBAAmB,CAAC,YAWzB;QACC,MAAM,cAAc,GAAmB;YACrC,OAAO,EAAE,YAAY,CAAC,OAAO;YAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,CAAC;YACpC,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,YAAY,EAAE,YAAY,CAAC,YAAY;YACvC,KAAK,EAAE,YAAY,CAAC,KAAK;YACzB,OAAO,EAAE,YAAY,CAAC,OAAO;SAC9B,CAAC;QAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,OAAe,EACf,SAAgB,EAChB,OAAc,EACd,QAAgB,GAAG;QAEnB,MAAM,KAAK,GAAG,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACtE,MAAM,GAAG,GAAG,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;QAElC,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACzE,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAEnE,MAAM,CACJ,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,sBAAsB,EACtB,eAAe,CAChB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oBACtB,KAAK,EAAE;wBACL,MAAM,EAAE,aAAa;qBACtB;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBAC5B,KAAK,EAAE;wBACL,SAAS,EAAE;4BACT,GAAG,EAAE,KAAK;yBACX;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;oBAC5B,KAAK,EAAE;wBACL,SAAS,EAAE;4BACT,GAAG,EAAE,QAAQ;yBACd;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;oBAChC,IAAI,EAAE;wBACJ,QAAQ,EAAE,IAAI;qBACf;oBACD,KAAK,EAAE;wBACL,SAAS,EAAE;4BACT,GAAG,EAAE,QAAQ;yBACd;qBACF;iBACF,CAAC;aACH,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ,EAAE;oBACR,WAAW;oBACX,YAAY;oBACZ,mBAAmB;oBACnB,sBAAsB;oBACtB,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;oBACnD,gBAAgB,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC3E;gBACD,MAAM,EAAE;oBACN,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC;oBACpD,YAAY,EAAE,GAAG;oBACjB,aAAa,EAAE,WAAW;iBAC3B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,OAAe,EACf,QAAgB,EAChB,SAAiB,EACjB,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,cAAc,GAAmB;gBACrC,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;YAEtE,OAAO;gBACL,OAAO,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC;gBAChC,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAC7C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAC5D,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,EAAE,EAAE,UAAU;qBACf;iBACF;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,CAAC,KAAK,+BAA+B,UAAU,QAAQ,CAAC,CAAC;YAC/F,OAAO,YAAY,CAAC,KAAK,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,cAA8B;QAC9D,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACnC,IAAI,EAAE;gBACJ,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,QAAQ,EAAE,cAAc,CAAC,QAAQ;gBACjC,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,OAAO,EAAE,cAAc,CAAC,YAAY;gBACpC,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,cAA8B;QAC/D,MAAM,QAAQ,GAAG,oBAAoB,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;YAClC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE;YACjD,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,OAAO,EAAE,cAAc,CAAC,OAAO;SAChC,CAAC,CAAC;QAEH,MAAM,mBAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,mBAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC1D,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,EAAE,CAAC;YAEjC,MAAM,SAAS,GAAG,MAAM,mBAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,cAAc,GAAG,SAAS;iBAC7B,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC;iBACrC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACd,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAS,CAAC,CAAC;oBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,OAAO;wBACL,OAAO;wBACP,GAAG,MAAM;qBACV,CAAC;gBACJ,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC,CAAC;iBACD,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;YAEzC,OAAO,cAAc,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,mBAAW,CAAC,QAAQ,EAAE,CAAC;YAExC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,QAAQ,GAAG,oBAAoB,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACxD,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,cAA8B;QAClE,IAAI,CAAC;YAEH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBACrD,KAAK,EAAE;oBACL,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,MAAM,EAAE,aAAa;oBACrB,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE;oBAC9B,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE;iBAC7B;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE;wBACL,OAAO,EAAE;4BACP,IAAI,EAAE,IAAI;yBACX;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpD,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,WAAkB,CAAC;YACxD,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACxE,OAAO,EAAE,CAAC;YACZ,CAAC;YAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CACrC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,EACxB,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS,CACrB,CAAC;YAGF,IAAI,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,SAAS,GAAsB;oBACnC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,IAAI,cAAc,CAAC,OAAO,EAAE;oBACvD,OAAO,EAAE,cAAc,CAAC,OAAO;oBAC/B,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACrF,MAAM,EAAE,YAAY,CAAC,MAAM;oBAC3B,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI;oBAChC,QAAQ,EAAE;wBACR,QAAQ,EAAE,cAAc,CAAC,QAAQ;wBACjC,SAAS,EAAE,cAAc,CAAC,SAAS;qBACpC;oBACD,QAAQ;oBACR,aAAa,EAAE,UAAU,CAAC,MAAM;oBAChC,SAAS,EAAE,cAAc,CAAC,SAAS;oBACnC,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC;iBACvE,CAAC;gBAGF,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;gBAE7C,OAAO,CAAC,SAAS,CAAC,CAAC;YACrB,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY;QAC9E,MAAM,CAAC,GAAG,MAAM,CAAC;QACjB,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAChC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAChC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QACzC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEzC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEzD,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAEO,0BAA0B,CAAC,QAAgB,EAAE,aAAqB;QACxE,MAAM,cAAc,GAAG,QAAQ,GAAG,aAAa,CAAC;QAChD,MAAM,gBAAgB,GAAG,CAAC,cAAc,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;QAEhE,IAAI,gBAAgB,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC;QAC1C,IAAI,gBAAgB,GAAG,EAAE;YAAE,OAAO,QAAQ,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,SAA4B;QAC/D,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE;gBACJ,KAAK,EAAE,wBAAwB,SAAS,CAAC,QAAQ,EAAE;gBACnD,WAAW,EAAE,SAAS,SAAS,CAAC,SAAS,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,yBAAyB,SAAS,CAAC,aAAa,UAAU;gBACxI,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,MAAM,EAAE,MAAM;gBACd,UAAU,EAAE,SAAS,CAAC,SAAS;gBAC/B,YAAY,EAAE,SAAS,CAAC,OAAO;gBAC/B,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC5C,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;aACpC;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;CACF;AAED,kBAAe,eAAe,CAAC"}