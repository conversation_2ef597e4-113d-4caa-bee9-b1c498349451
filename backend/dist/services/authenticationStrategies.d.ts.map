{"version": 3, "file": "authenticationStrategies.d.ts", "sourceRoot": "", "sources": ["../../src/services/authenticationStrategies.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAuB,MAAM,eAAe,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAgB,MAAM,gBAAgB,CAAC;AAQpE,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,QAAQ,CAAC;IACf,MAAM,EAAE,UAAU,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,oBAAoB,EAAE,MAAM,CAAC;IAC7B,WAAW,CAAC,EAAE;QACZ,YAAY,CAAC,EAAE,GAAG,CAAC;QACnB,aAAa,CAAC,EAAE,GAAG,CAAC;QACpB,YAAY,CAAC,EAAE,GAAG,CAAC;KACpB,CAAC;CACH;AAKD,MAAM,WAAW,sBAAsB;IAMrC,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAOxD,SAAS,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC;IAMzC,uBAAuB,IAAI,MAAM,CAAC;CACnC;AAKD,qBAAa,yBAA0B,YAAW,sBAAsB;IACtE,OAAO,CAAC,WAAW,CAAM;IACzB,OAAO,CAAC,MAAM,CAAe;;IAO7B,SAAS,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO;IAIxC,uBAAuB,IAAI,MAAM;IAI3B,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;YA4E/C,mBAAmB;CAyClC;AAKD,qBAAa,2BAA4B,YAAW,sBAAsB;IACxE,OAAO,CAAC,MAAM,CAAe;;IAM7B,SAAS,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO;IAIxC,uBAAuB,IAAI,MAAM;IAI3B,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;CA2E9D;AAKD,qBAAa,iCAAkC,YAAW,sBAAsB;IAC9E,OAAO,CAAC,MAAM,CAAe;;IAS7B,OAAO,CAAC,iBAAiB;IASzB,OAAO,CAAC,sBAAsB;IAmB9B,SAAS,CAAC,SAAS,EAAE,SAAS,GAAG,OAAO;IAIxC,uBAAuB,IAAI,MAAM;IAI3B,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;CAmJ9D;AAKD,qBAAa,6BAA6B;IACxC,OAAO,CAAC,UAAU,CAA2B;;IAe7C,WAAW,CAAC,SAAS,EAAE,SAAS,GAAG,sBAAsB,GAAG,IAAI;IAQhE,gBAAgB,IAAI,sBAAsB,EAAE;IAQ5C,WAAW,CAAC,QAAQ,EAAE,sBAAsB,GAAG,IAAI;IAQnD,cAAc,CAAC,oBAAoB,EAAE,MAAM,GAAG,IAAI;CAKnD;AAKD,eAAO,MAAM,6BAA6B,+BAAsC,CAAC"}