{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAG1D,OAAO,EAAgB,QAAQ,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAKpE,UAAU,iBAAiB;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,QAAQ,CAAC;IACf,MAAM,EAAE,UAAU,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW,CAAC,EAAE;QACZ,YAAY,CAAC,EAAE,GAAG,CAAC;QACnB,aAAa,CAAC,EAAE,GAAG,CAAC;QACpB,YAAY,CAAC,EAAE,GAAG,CAAC;KACpB,CAAC;CACH;AAGD,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,OAAO,CAAC;QAChB,UAAU,OAAO;YACf,IAAI,CAAC,EAAE;gBACL,MAAM,EAAE,MAAM,CAAC;gBACf,SAAS,EAAE,MAAM,CAAC;gBAClB,MAAM,EAAE,GAAG,CAAC;gBACZ,KAAK,EAAE,MAAM,CAAC;aACf,CAAC;YACF,IAAI,CAAC,EAAE,iBAAiB,CAAC;SAC1B;KACF;CACF;AAGD,qBAAa,mBAAoB,SAAQ,KAAK;IAGnC,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,MAAM;gBAFzB,OAAO,EAAE,MAAM,EACR,IAAI,EAAE,MAAM,EACZ,UAAU,GAAE,MAAY;CAKlC;AAED,qBAAa,kBAAmB,SAAQ,KAAK;IAGlC,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,MAAM;gBAFzB,OAAO,EAAE,MAAM,EACR,IAAI,EAAE,MAAM,EACZ,UAAU,GAAE,MAAY;CAKlC;AAOD,qBAAa,qBAAqB;IAChC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAwB;IAE/C,MAAM,CAAC,WAAW,IAAI,qBAAqB;IAU3C,OAAO,CAAC,YAAY;YAWN,kBAAkB;YA6ClB,mBAAmB;YA6CnB,mBAAmB;IAqG3B,mBAAmB,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;QAAE,IAAI,EAAE,GAAG,CAAC;QAAC,IAAI,EAAE,iBAAiB,CAAA;KAAE,CAAC;CAmDzF;AAGD,eAAO,MAAM,WAAW,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,4DA0ChF,CAAC;AAcF,eAAO,MAAM,WAAW,GAAI,cAAc,QAAQ,EAAE,EAAE,UAAU;IAC9D,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC5B,kBAAkB,CAAC,EAAE,MAAM,CAAC;CAC7B,MACe,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,4DAyF9D,CAAC;AAGF,eAAO,MAAM,YAAY,QA5FJ,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DA4FC,CAAC;AACjE,eAAO,MAAM,iBAAiB,QA7FT,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DA6FyB,CAAC;AACzF,eAAO,MAAM,aAAa,QA9FL,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DA8FX,CAAC;AACrD,eAAO,MAAM,YAAY,QA/FJ,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DA+FU,CAAC;AAC1E,eAAO,MAAM,iBAAiB,QAhGT,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DAgGM,CAAC;AAGtE,eAAO,MAAM,kBAAkB,GAAI,aAAa,MAAM,EAAE,WAnGnC,OAAO,OAAO,QAAQ,QAAQ,YAAY,4DAqG9D,CAAC;AAGF,eAAO,MAAM,YAAY,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,kBAgBjF,CAAC;AAGF,qBAAa,cAAc;IACzB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAiB;IAExC,MAAM,CAAC,WAAW,IAAI,cAAc;IAO9B,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;IAqBhE,eAAe,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAkBvD,aAAa,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAUlD,qBAAqB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAS3D;AAGD,eAAO,MAAM,iBAAiB,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAmBhF,CAAC;AAGF,eAAO,MAAM,QAAQ,GAAI,QAAQ,MAAM,MACvB,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,kBAiC9D,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,OAAO,GAAG,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDAkD1F,CAAC;AAGF,eAAO,MAAM,eAAe,GAAU,KAAK,OAAO,EAAE,KAAK,QAAQ,kBAkChE,CAAC;;;;uBAhZqC,OAAO,OAAO,QAAQ,QAAQ,YAAY;gCAwDvC,QAAQ,EAAE,YAAY;QAC9D,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;QAC5B,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC7B,MACe,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY;wBAA1C,OAAO,OAAO,QAAQ,QAAQ,YAAY;6BAA1C,OAAO,OAAO,QAAQ,QAAQ,YAAY;yBAA1C,OAAO,OAAO,QAAQ,QAAQ,YAAY;wBAA1C,OAAO,OAAO,QAAQ,QAAQ,YAAY;6BAA1C,OAAO,OAAO,QAAQ,QAAQ,YAAY;sCAmGf,MAAM,EAAE,WAnGnC,OAAO,OAAO,QAAQ,QAAQ,YAAY;wBAwGvB,OAAO,OAAO,QAAQ,QAAQ,YAAY;6BA0F3C,OAAO,OAAO,QAAQ,QAAQ,YAAY;uBAsBhD,MAAM,MACvB,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY;6BAoCxB,GAAG,OAAO,OAAO,OAAO,QAAQ,QAAQ,YAAY;2BAqDhD,OAAO,OAAO,QAAQ;;;;AAqCjE,wBAkBE"}