{"version": 3, "file": "geofence.js", "sourceRoot": "", "sources": ["../../src/routes/geofence.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,yDAAyE;AACzE,2CAA8C;AAC9C,6CAA+D;AAE/D,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAChC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,MAAM,sBAAsB,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;IACzG,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE;IAC5B,kBAAW;IACX,mBAAY;IACZ,IAAA,yBAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;IACnC,IAAA,yBAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;CACvC,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAC1C,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;QAE3C,MAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QACD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;QAChD,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3C,KAAK;YACL,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,OAAO,EAAE,IAAI;qBACd;iBACF;aACF;YACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC/B,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAEnB,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,KAAK,CAAC,MAAM;SACpB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,gCAAgC;aAC1C;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE;IACjC,kBAAW;IACX,mBAAY;IACZ,IAAA,yBAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACzC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACvC,IAAA,yBAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;IACnC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;CACrC,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACzI,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QACvF,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAC1C,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC;QAE5C,MAAM,KAAK,GAAQ;YACjB,SAAS,EAAE;gBACT,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,OAAO;aACb;SACF,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACzD,KAAK;YACL,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;6BACX;yBACF;qBACF;iBACF;aACF;YACD,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAChC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAEnB,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,UAAU,CAAC,MAAM;SACzB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,qCAAqC;aAC/C;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,MAAM,CAAC,GAAG,CAAC,uBAAuB,EAAE;IAClC,kBAAW;IACX,mBAAY;IACZ,IAAA,yBAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACzC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACvC,IAAA,yBAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;CACpC,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QACzI,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QACvF,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAE1C,MAAM,KAAK,GAAQ;YACjB,SAAS,EAAE;gBACT,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,OAAO;aACb;SACF,CAAC;QAEF,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC3D,KAAK;YACL,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;gCACd,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;6BACX;yBACF;qBACF;iBACF;aACF;YACD,OAAO,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE;SACjC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;QAEnB,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,WAAW,CAAC,MAAM;SAC1B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,sCAAsC;aAChD;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}