{"version": 3, "file": "tokenTypeDetector.js", "sourceRoot": "", "sources": ["../../src/utils/tokenTypeDetector.ts"], "names": [], "mappings": ";;;AAsLA,0DAEC;AAxLD,wCAA6D;AAK7D,MAAa,qBAAqB;IAMhC,eAAe,CAAC,KAAa;QAC3B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAExC,OAAO,gBAAS,CAAC,GAAG,CAAC;QACvB,CAAC;QAGD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAGlC,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,OAAO,gBAAS,CAAC,WAAW,CAAC;QAC/B,CAAC;QAGD,IAAI,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,gBAAS,CAAC,GAAG,CAAC;QACvB,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YACpC,OAAO,gBAAS,CAAC,KAAK,CAAC;QACzB,CAAC;QAGD,OAAO,gBAAS,CAAC,GAAG,CAAC;IACvB,CAAC;IAOO,YAAY,CAAC,KAAa;QAEhC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;CACF;AA5CD,sDA4CC;AAKD,MAAa,iBAAiB;IAM5B,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAG7B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;IAC5B,CAAC;IAOD,MAAM,CAAC,YAAY,CAAC,KAAa;QAC/B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAG7B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAOD,MAAM,CAAC,kBAAkB,CAAC,KAAa;QACrC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAOD,MAAM,CAAC,wBAAwB,CAAC,KAAa;QAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACjD,CAAC;IAQD,MAAM,CAAC,mBAAmB,CAAC,KAAa,EAAE,YAAuB;QAC/D,QAAQ,YAAY,EAAE,CAAC;YACrB,KAAK,gBAAS,CAAC,GAAG;gBAChB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,gBAAS,CAAC,KAAK;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,gBAAS,CAAC,WAAW;gBACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACxC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAOD,MAAM,CAAC,mBAAmB,CAAC,KAAa;QACtC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,OAAO,aAAa,OAAO,CAAC,MAAM,SAAS,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC,CAAC,CAAC,aAAa,KAAK,GAAG,CAAC,CAAC,CAAC,2BAA2B,CAAC;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;YAE/B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;gBACrC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;gBAClC,CAAC,CAAC,KAAK,CAAC;YACV,OAAO,eAAe,WAAW,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;QACnD,CAAC;QAED,OAAO,iBAAiB,OAAO,CAAC,MAAM,SAAS,CAAC;IAClD,CAAC;CACF;AA3HD,8CA2HC;AAKD,SAAgB,uBAAuB;IACrC,OAAO,IAAI,qBAAqB,EAAE,CAAC;AACrC,CAAC;AAKY,QAAA,iBAAiB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}