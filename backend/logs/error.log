{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:14.116Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:14.184Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:14.329Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:14.495Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:14.706Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:15.067Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:15.389Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:15.770Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:16.181Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:16.646Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:17.207Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:17.830Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:18.518Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:19.354Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:20.118Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:20.891Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:21.710Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:22.621Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:23.535Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:24.506Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"bahinlink-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)","syscall":"connect","timestamp":"2025-07-16T23:59:25.636Z"}
{"error":{"clerkError":true,"clerkTraceId":"c83bb9509f85e1f7b8b1e91f2596f62f","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:05.682Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"eebe9bebbf621b501fd6def0deee590f","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:11.957Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"844e58162d03d200b8ec112c5b36bfec","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:20.150Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"da26f9dad25f840af028d40b99c4dd54","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:21.155Z","token":"<EMAIL>..."}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:33.480Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:25:36.233Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:04.910Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:08.122Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:29.874Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:32.378Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:33.211Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:34.735Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:38.139Z"}
{"clerkUserId":"user_2zlLX61gzusTJy1tDjod3AM97z7","error":{"clientVersion":"5.22.0","code":"P2002","meta":{"modelName":"User","target":["email"]},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Failed to sync user from Clerk","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:41.939Z"}
{"error":{"clerkError":true,"clerkTraceId":"2f1177eb1c17aac521bc181cb7c61494","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:26:59.934Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"e26b54ecde0023050b21bc1fe6408254","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:27:03.797Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"e819ac2bbee8794ade552896c90a7e30","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:27:14.362Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"814655c4a286a00c19f2b46f8e98604d","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:27:15.610Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"eeb1f092fe3df28ea6fdd66f499a0fda","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:27:59.401Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"402bc3b6c5afc0e1165fb820848db636","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:28:00.601Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"726fd89bda5cef56b4c277ed30b4e084","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:13.529Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"ae02f45ffcb8f20faa531f90fc7abeed","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:15.810Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"b0ecb90da7557cf2f06dace86bf42c00","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:28.326Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"f80d4b945ff375341dbdd06976735989","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:31.527Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"1d98e6af951d1883468dddb58771b754","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:33.352Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"4c23f3a21e17388ecfec1a050c249527","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:32:33.877Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"32b22c39242a006463296cffb143fd20","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:33:14.037Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"6489605890c01bfc82b2300c4d2206d6","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:33:15.302Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"46bebdd0e26a05b2273ecf718fc88eae","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:34:19.318Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"99d5ca13849f27fe482d858b186ffbb1","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:34:25.099Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"971b15dc7cb2459626a11b9356ba8c7b","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:35:19.314Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"fdb75fa7947e447d054a07d0ec80eec7","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:35:20.520Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"951a3e5f4c20847520168d64f9605202","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:36:17.408Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"73b9fbb7091e21b8dbc2ce7e729aa312","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:36:19.394Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"7729d0fd44f591547b48e4894db233df","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:36:58.416Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"f6a2cdfa332118357b48e4894db2399d","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:36:59.619Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"","errors":[{"code":"unexpected_error","message":"fetch failed"}]},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:37:25.112Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"4e8193904773f2bac61b8bd37ea40bbc","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:37:30.629Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"8638667a2854cf100d5b7e663b1718ce","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:38:08.898Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"ae663e4f76c977ae3685214426077065","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:38:20.262Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"b085312f16a3c97cd7205ce6529a6b87","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:39:12.689Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"79498dd25437e5eac360c4d22bc8a5cc","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:39:13.983Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"41c243c0333eb86e947e99e493844852","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:40:12.883Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"9588fa0c9e789eb75e1ab664f2874c57","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:40:14.155Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"2d723f089514a79aeeefd32cbda358c7","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:40:16.893Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"7f319c210f51228ef179e68d9971acde","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:40:17.752Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"cb4e47bf95b8f09d9666a7714aa320f2","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:41:18.078Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"fe101c4bf5d3743b10d727aed73f8f02","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:41:30.072Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"7296a0763e42ff5857864e31c5c2842d","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:45:27.057Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"5fd00aff95a533a2ad10a3500b5ed95e","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:45:28.712Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"fe3d26356ed8209e04aed0e61ec1f931","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:47:25.818Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"04c2abdb636236d13cca63c6446927ee","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:47:42.671Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"d42ec6a02cbbe5995e681bcf81d82e31","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:51:06.108Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"59ac2a1e9add160b672ef0c76801b907","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:51:08.321Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"81e17b99be6d3daaecb279b5926dd226","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:51:14.416Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"a047d2ccc52756113a86c236ce5bba3a","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:51:15.861Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"d4a2896dd5f66499ab3b0d223b55b643","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:52:44.842Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"dda9b9e96c7c508efde87fc96b00f79d","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:52:46.697Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"9cc96d0cfd99525cbc39404b9deee33f","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:53:16.797Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"b829f9c8dc58e30730244bd83e2dec94","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:53:19.663Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"36f8d7f493198db8cecfa90616a609fe","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:54:28.900Z","token":"<EMAIL>..."}
{"error":{"clerkError":true,"clerkTraceId":"65ae0b130639bde8d862440eb6603465","errors":[{"code":"resource_not_found","longMessage":"No user was found <NAME_EMAIL>","message":"not found","meta":{}}],"status":404},"level":"error","message":"Clerk token validation failed","service":"bahinlink-backend","timestamp":"2025-07-17T01:54:30.494Z","token":"<EMAIL>..."}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.061Z","tokenLength":47}
{"email":"not-an-email","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.413Z"}
{"email":"","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.446Z"}
{"email":"test@","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.475Z"}
{"email":"@example.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.527Z"}
{"error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.616Z","token":"not-dev-token..."}
{"error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.642Z","token":"<EMAIL>..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.681Z","token":"dev:not-an-email..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.699Z","token":"dev:..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:39:48.727Z","token":"dev:test@..."}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.341Z","tokenLength":47}
{"email":"not-an-email","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.477Z"}
{"email":"","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.493Z"}
{"email":"test@","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.508Z"}
{"email":"@example.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.523Z"}
{"error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.586Z","token":"not-dev-token..."}
{"error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.604Z","token":"<EMAIL>..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.640Z","token":"dev:not-an-email..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.655Z","token":"dev:..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:44:34.663Z","token":"dev:test@..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:35.836Z","tokenLength":47}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:35.841Z","tokenLength":47,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:42.788Z","tokenLength":30}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:42.790Z","tokenLength":30,"tokenPrefix":"unsupporte..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.456Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.457Z","tokenLength":20,"tokenPrefix":"invalid-em..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.463Z","token":"dev:invalid-email..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.464Z","tokenLength":17,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.795Z","tokenLength":18}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:56:46.796Z","tokenLength":18,"tokenPrefix":"@invalid-e..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:03.922Z","tokenLength":47}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:03.924Z","tokenLength":47,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:07.520Z","tokenLength":30}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:07.521Z","tokenLength":30,"tokenPrefix":"unsupporte..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.330Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.334Z","tokenLength":20,"tokenPrefix":"invalid-em..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.340Z","token":"dev:invalid-email..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.342Z","tokenLength":17,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.656Z","tokenLength":13}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T02:59:12.662Z","tokenLength":13,"tokenPrefix":"invalid@em..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:18.585Z","tokenLength":47}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:18.587Z","tokenLength":47,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:21.639Z","tokenLength":30}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:21.642Z","tokenLength":30,"tokenPrefix":"unsupporte..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:25.242Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:25.244Z","tokenLength":20,"tokenPrefix":"invalid-em..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:25.250Z","token":"dev:invalid-email..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:01:25.251Z","tokenLength":17,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:17.044Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:17.047Z","tokenLength":20,"tokenPrefix":"invalid-em..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:17.072Z","token":"dev:invalid-email..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:17.074Z","tokenLength":17,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:20.555Z","tokenLength":13}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:20.557Z","tokenLength":13,"tokenPrefix":"invalid-to..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:21.735Z","tokenLength":13}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:21.736Z","tokenLength":13,"tokenPrefix":"invalid-to..."}
{"error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:22.594Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:21:22.596Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:36.753Z","token":"dev:prod-reject-1752..."}
{"error":"Development authentication failed: Development authentication is not enabled in this environment","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:36.755Z","tokenLength":41,"tokenPrefix":"dev:prod-r..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:47.328Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:47.329Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:47.351Z","token":"dev:..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:47:47.358Z","tokenLength":4,"tokenPrefix":"dev:..."}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:39.947Z","token":"dev:prod-reject-1752..."}
{"error":"Development authentication failed: Development authentication is not enabled in this environment","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:39.949Z","tokenLength":41,"tokenPrefix":"dev:prod-r..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:50.375Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:50.376Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:50.401Z","token":"dev:..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T03:50:50.404Z","tokenLength":4,"tokenPrefix":"dev:..."}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:54:47.820Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:54:47.923Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:54:47.931Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:54:47.945Z","tokenLength":31}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:07.004Z","tokenLength":69}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:07.347Z","tokenLength":70}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:13.500Z","tokenLength":49}
{"email":"not-an-email","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.138Z"}
{"email":"@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.141Z"}
{"email":"user@","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.142Z"}
{"email":"user@domain","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.145Z"}
{"email":"user.domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.150Z"}
{"email":"user@@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.153Z"}
{"email":"user@domain@com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.155Z"}
{"email":"","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.159Z"}
{"email":"   ","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.167Z"}
{"email":"<NAME_EMAIL>","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:30.169Z"}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.514Z","token":"dev:env-test-1752764..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.547Z","token":"not-dev-token..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.552Z","token":"development:user@exa..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.554Z","token":"dev:..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.560Z","token":"dev:invalid-email..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.569Z","token":"dev:user@..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.571Z","token":"dev:@domain.com..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.573Z","token":"dev:user@domain..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.576Z","token":"dev:user.domain.com..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:55:51.578Z","token":"dev:user with spaces..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:37.480Z","tokenLength":34}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:37.483Z","tokenLength":34,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:57.606Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:57.608Z","tokenLength":20,"tokenPrefix":"invalid-to..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:57.625Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:57.627Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:58.040Z","tokenLength":15}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T14:58:58.227Z","tokenLength":15,"tokenPrefix":"eyJ.invali..."}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:18:57.941Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:18:57.974Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:18:57.981Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:18:57.996Z","tokenLength":31}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:18:59.787Z","tokenLength":31}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:00.926Z","tokenLength":20}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:01.246Z","tokenLength":21}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:01.598Z","tokenLength":39}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:01.920Z","tokenLength":31}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:02.296Z","tokenLength":29}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:02.598Z","tokenLength":18}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:02.948Z","tokenLength":49}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:03.257Z","tokenLength":47}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.128Z","tokenLength":30008}
{"email":"not-an-email","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.139Z"}
{"email":"@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.143Z"}
{"email":"user@","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.145Z"}
{"email":"user@domain","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.148Z"}
{"email":"user.domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.150Z"}
{"email":"user@@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.152Z"}
{"email":"user@domain@com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.154Z"}
{"email":"user@domain.","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:04.157Z"}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:21.514Z","token":"not-dev-token..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:21.517Z","token":"development:user@exa..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:21.520Z","token":"dev:..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:21.523Z","token":"dev..."}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:23.163Z","token":"dev:env-restriction-..."}
{"developmentMode":false,"environment":"staging","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:23.167Z","token":"dev:env-restriction-..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:23.528Z","token":"dev:invalid-email-3..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:23.535Z","tokenLength":19,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.358Z","tokenLength":15}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.360Z","tokenLength":15,"tokenPrefix":"invalid-em..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.422Z","tokenLength":17}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.423Z","tokenLength":17,"tokenPrefix":"malformed-..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.701Z","tokenLength":15}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.703Z","tokenLength":15,"tokenPrefix":"invalid-em..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.725Z","tokenLength":32}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.727Z","tokenLength":32,"tokenPrefix":"eyJhbGciOi..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.740Z","token":"dev:invalid-dev-emai..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:24.743Z","tokenLength":21,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:25.098Z","tokenLength":32}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:25.104Z","tokenLength":32,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:25.108Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:25.109Z","tokenLength":20,"tokenPrefix":"invalid-em..."}
{"error":"Request-URI Too Large","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:27.385Z","tokenLength":1000000}
{"error":"JWT authentication failed: Request-URI Too Large","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:27.386Z","tokenLength":1000000,"tokenPrefix":"aaaaaaaaaa..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:30.879Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:30.881Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:30.943Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:30.945Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.079Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.081Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.102Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.106Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.116Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.117Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.129Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.132Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.145Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.146Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.194Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.196Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"email":"<EMAIL>","error":"\nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.209Z"}
{"error":"Email authentication failed: \nInvalid `this.prisma.user.create()` invocation in\nC:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\backend\\src\\services\\authenticationStrategies.ts:232:39\n\n  229 if (!user) {\n  230   logger.info('Creating new user for email authentication', { email: token });\n  231   \n→ 232   user = await this.prisma.user.create(\nUnique constraint failed on the fields: (`email`)","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:36.210Z","tokenLength":31,"tokenPrefix":"rapid-1752..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:19:47.084Z","tokenLength":27}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:35.600Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:35.701Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:35.709Z","tokenLength":31}
{"error":"Clerk configuration invalid","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:35.716Z","tokenLength":31}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:42.735Z","tokenLength":69}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:43.128Z","tokenLength":70}
{"error":"Unauthorized","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:48.553Z","tokenLength":49}
{"email":"not-an-email","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.416Z"}
{"email":"@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.419Z"}
{"email":"user@","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.421Z"}
{"email":"user@domain","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.424Z"}
{"email":"user.domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.426Z"}
{"email":"user@@domain.com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.428Z"}
{"email":"user@domain@com","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.433Z"}
{"email":"","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.435Z"}
{"email":"   ","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.437Z"}
{"email":"<NAME_EMAIL>","error":"Invalid email format","level":"error","message":"Email authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:48:56.439Z"}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.620Z","token":"dev:env-test-1752767..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.631Z","token":"not-dev-token..."}
{"developmentMode":true,"environment":"test","error":"Invalid development token format","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.633Z","token":"development:user@exa..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.638Z","token":"dev:..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.641Z","token":"dev:invalid-email..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.643Z","token":"dev:user@..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.646Z","token":"dev:@domain.com..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.648Z","token":"dev:user@domain..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.649Z","token":"dev:user.domain.com..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:49:07.655Z","token":"dev:user with spaces..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:11.757Z","tokenLength":34}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:11.758Z","tokenLength":34,"tokenPrefix":"eyJhbGciOi..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.032Z","tokenLength":20}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.034Z","tokenLength":20,"tokenPrefix":"invalid-to..."}
{"developmentMode":true,"environment":"test","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.053Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.055Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"error":"Not Found","level":"error","message":"JWT authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.414Z","tokenLength":15}
{"error":"JWT authentication failed: Not Found","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:50:29.415Z","tokenLength":15,"tokenPrefix":"eyJ.invali..."}
{"developmentMode":false,"environment":"production","error":"Development authentication is not enabled in this environment","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:42.126Z","token":"dev:prod-reject-1752..."}
{"error":"Development authentication failed: Development authentication is not enabled in this environment","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:42.129Z","tokenLength":41,"tokenPrefix":"dev:prod-r..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:53.092Z","token":"dev:invalid-email-fo..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:53.094Z","tokenLength":24,"tokenPrefix":"dev:invali..."}
{"developmentMode":true,"environment":"development","error":"Invalid email format in development token","level":"error","message":"Development authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:53.118Z","token":"dev:..."}
{"error":"Development authentication failed: Invalid email format in development token","level":"error","message":"Authentication failed","service":"bahinlink-backend","timestamp":"2025-07-17T15:51:53.124Z","tokenLength":4,"tokenPrefix":"dev:..."}
