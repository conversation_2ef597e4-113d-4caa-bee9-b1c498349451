{"version": 2, "name": "bahinlink-unified-system", "builds": [{"src": "client-portal/package.json", "use": "@vercel/static-build", "config": {"distDir": "build", "buildCommand": "npm ci && npm run build"}}, {"src": "admin-portal/package.json", "use": "@vercel/static-build", "config": {"distDir": "build", "buildCommand": "npm ci && npm run build"}}], "rewrites": [{"source": "/api/(.*)", "destination": "/backend/dist/server.js"}, {"source": "/admin/(.*)", "destination": "/admin-portal/index.html"}, {"source": "/admin-static/(.*)", "destination": "/admin-portal/static/$1"}, {"source": "/(.*)", "destination": "/client-portal/index.html"}], "env": {"REACT_APP_API_URL": "https://finalagent-main-eywj.vercel.app/api", "REACT_APP_ADMIN_PORTAL_URL": "https://finalagent-main-eywj.vercel.app/admin", "REACT_APP_CLIENT_PORTAL_URL": "https://finalagent-main-eywj.vercel.app", "REACT_APP_CLERK_PUBLISHABLE_KEY": "pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk", "REACT_APP_NAME": "BahinLink", "REACT_APP_VERSION": "1.0.0", "GENERATE_SOURCEMAP": "false", "ESLINT_NO_DEV_ERRORS": "true", "TSC_COMPILE_ON_ERROR": "true", "SKIP_PREFLIGHT_CHECK": "true"}}