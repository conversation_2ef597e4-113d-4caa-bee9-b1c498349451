# Nginx Configuration for Localhost Development
# This configuration unifies all services under localhost with different sub-paths

# Upstream servers for development
upstream backend_dev {
    server localhost:8000;
    keepalive 32;
}

upstream admin_portal_dev {
    server localhost:3001;
    keepalive 32;
}

upstream client_portal_dev {
    server localhost:3003;
    keepalive 32;
}

# Main server configuration for localhost development
server {
    listen 80;
    server_name localhost;
    
    # Increase client body size for file uploads
    client_max_body_size 50M;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API routes - proxy all /api requests to backend on port 8000
    location /api/ {
        proxy_pass http://backend_dev;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # CORS headers for development
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # WebSocket support for real-time features
    location /socket.io/ {
        proxy_pass http://backend_dev;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Admin Portal - proxy /admin/ requests to port 3001
    location /admin/ {
        proxy_pass http://admin_portal_dev/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle React Router for SPA
        try_files $uri $uri/ @admin_fallback;
    }
    
    # Admin Portal fallback for React Router
    location @admin_fallback {
        proxy_pass http://admin_portal_dev/index.html;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Client Portal - proxy /client/ requests to port 3003
    location /client/ {
        proxy_pass http://client_portal_dev/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle React Router for SPA
        try_files $uri $uri/ @client_fallback;
    }
    
    # Client Portal fallback for React Router
    location @client_fallback {
        proxy_pass http://client_portal_dev/index.html;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # File uploads and static assets from backend
    location /uploads/ {
        proxy_pass http://backend_dev/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # Try to serve from admin portal first, then client portal, then backend
        try_files $uri @try_admin_static;
    }
    
    location @try_admin_static {
        proxy_pass http://admin_portal_dev$uri;
        proxy_intercept_errors on;
        error_page 404 = @try_client_static;
    }
    
    location @try_client_static {
        proxy_pass http://client_portal_dev$uri;
        proxy_intercept_errors on;
        error_page 404 = @try_backend_static;
    }
    
    location @try_backend_static {
        proxy_pass http://backend_dev$uri;
    }
    
    # Default route - redirect to backend (landing page or API documentation)
    location = / {
        proxy_pass http://backend_dev/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Catch-all for any other requests - send to backend
    location / {
        proxy_pass http://backend_dev;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        return 404 '{"error": "Not Found", "message": "The requested resource was not found"}';
        add_header Content-Type application/json;
    }
    
    location = /50x.html {
        return 500 '{"error": "Internal Server Error", "message": "Something went wrong"}';
        add_header Content-Type application/json;
    }
}