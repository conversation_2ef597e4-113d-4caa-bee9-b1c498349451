import { 
  mobileAPI,
  isAuthenticationAvailable, 
  getCurrentTokenInfo
} from '../api';

// Mock React Native modules for testing
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

describe('End-to-End Authentication Flow Tests - Mobile App', () => {
  beforeEach(() => {
    // Clear any global state
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Mobile Token Provider Integration', () => {
    it('should check authentication availability', async () => {
      const authAvailable = await isAuthenticationAvailable();
      
      // Should return boolean without throwing
      expect(typeof authAvailable).toBe('boolean');
      console.log('Mobile auth available:', authAvailable);
    });

    it('should get current token info', async () => {
      const tokenInfo = await getCurrentTokenInfo();
      
      expect(tokenInfo).toHaveProperty('type');
      expect(tokenInfo).toHaveProperty('available');
      expect(typeof tokenInfo.available).toBe('boolean');
      
      console.log('Mobile token info:', tokenInfo);
    });
  });

  describe('Mobile API Endpoints', () => {
    it('should handle dashboard API calls', async () => {
      // Mock the API response
      const mockResponse = {
        data: {
          currentShift: {
            id: 'shift-1',
            siteId: 'site-1',
            siteName: 'Test Site',
            status: 'ACTIVE'
          },
          todayStats: {
            hoursWorked: 8,
            patrolsCompleted: 5,
            reportsSubmitted: 3,
            incidentsReported: 1
          },
          notifications: {
            unreadCount: 2,
            urgentCount: 0
          }
        }
      };

      // Mock axios get method
      const mockAxios = {
        get: jest.fn().mockResolvedValue(mockResponse),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        patch: jest.fn(),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      // Replace the API client temporarily
      const originalGet = mobileAPI.getDashboard;
      mobileAPI.getDashboard = () => mockAxios.get('/mobile/dashboard');

      try {
        const response = await mobileAPI.getDashboard();
        
        expect(mockAxios.get).toHaveBeenCalledWith('/mobile/dashboard');
        expect(response.data.currentShift.status).toBe('ACTIVE');
        expect(response.data.todayStats.hoursWorked).toBe(8);
        expect(response.data.notifications.unreadCount).toBe(2);
        
        console.log('✓ Mobile dashboard API call successful');
      } finally {
        // Restore original method
        mobileAPI.getDashboard = originalGet;
      }
    });

    it('should handle patrol API calls', async () => {
      const mockResponse = {
        data: {
          patrols: [
            { id: 'patrol-1', status: 'ACTIVE', startTime: new Date().toISOString() },
            { id: 'patrol-2', status: 'COMPLETED', startTime: new Date().toISOString() }
          ]
        }
      };

      const mockAxios = {
        get: jest.fn().mockResolvedValue(mockResponse)
      };

      const originalGet = mobileAPI.getPatrols;
      mobileAPI.getPatrols = () => mockAxios.get('/mobile/patrols');

      try {
        const response = await mobileAPI.getPatrols();
        
        expect(mockAxios.get).toHaveBeenCalledWith('/mobile/patrols');
        expect(response.data.patrols).toHaveLength(2);
        expect(response.data.patrols[0].status).toBe('ACTIVE');
        
        console.log('✓ Mobile patrols API call successful');
      } finally {
        mobileAPI.getPatrols = originalGet;
      }
    });

    it('should handle reports API calls', async () => {
      const mockResponse = {
        data: [
          {
            id: 'report-1',
            type: 'PATROL',
            title: 'Patrol Report',
            status: 'SUBMITTED',
            createdAt: new Date().toISOString()
          },
          {
            id: 'report-2',
            type: 'INCIDENT',
            title: 'Incident Report',
            status: 'DRAFT',
            createdAt: new Date().toISOString()
          }
        ]
      };

      const mockAxios = {
        get: jest.fn().mockResolvedValue(mockResponse)
      };

      const originalGet = mobileAPI.getReports;
      mobileAPI.getReports = () => mockAxios.get('/mobile/reports');

      try {
        const response = await mobileAPI.getReports();
        
        expect(mockAxios.get).toHaveBeenCalledWith('/mobile/reports');
        expect(response.data).toHaveLength(2);
        expect(response.data[0].type).toBe('PATROL');
        expect(response.data[1].status).toBe('DRAFT');
        
        console.log('✓ Mobile reports API call successful');
      } finally {
        mobileAPI.getReports = originalGet;
      }
    });

    it('should handle shift management API calls', async () => {
      const mockStartResponse = {
        data: {
          success: true,
          shift: {
            id: 'shift-new',
            status: 'ACTIVE',
            startTime: new Date().toISOString()
          }
        }
      };

      const mockEndResponse = {
        data: {
          success: true,
          shift: {
            id: 'shift-1',
            status: 'COMPLETED',
            endTime: new Date().toISOString()
          }
        }
      };

      const mockAxios = {
        post: jest.fn()
          .mockResolvedValueOnce(mockStartResponse)
          .mockResolvedValueOnce(mockEndResponse)
      };

      const originalStart = mobileAPI.startShift;
      const originalEnd = mobileAPI.endShift;
      
      mobileAPI.startShift = (data: any) => mockAxios.post('/mobile/shifts/start', data);
      mobileAPI.endShift = (shiftId: string, data: any) => mockAxios.post(`/mobile/shifts/${shiftId}/end`, data);

      try {
        // Test start shift
        const startResponse = await mobileAPI.startShift({ siteId: 'site-1' });
        expect(mockAxios.post).toHaveBeenCalledWith('/mobile/shifts/start', { siteId: 'site-1' });
        expect(startResponse.data.success).toBe(true);
        expect(startResponse.data.shift.status).toBe('ACTIVE');

        // Test end shift
        const endResponse = await mobileAPI.endShift('shift-1', { notes: 'Shift completed' });
        expect(mockAxios.post).toHaveBeenCalledWith('/mobile/shifts/shift-1/end', { notes: 'Shift completed' });
        expect(endResponse.data.success).toBe(true);
        expect(endResponse.data.shift.status).toBe('COMPLETED');
        
        console.log('✓ Mobile shift management API calls successful');
      } finally {
        mobileAPI.startShift = originalStart;
        mobileAPI.endShift = originalEnd;
      }
    });

    it('should handle incident management API calls', async () => {
      const mockGetResponse = {
        data: [
          {
            id: 'incident-1',
            type: 'SECURITY',
            severity: 'HIGH',
            status: 'OPEN',
            createdAt: new Date().toISOString()
          }
        ]
      };

      const mockCreateResponse = {
        data: {
          success: true,
          incident: {
            id: 'incident-new',
            type: 'MAINTENANCE',
            severity: 'MEDIUM',
            status: 'OPEN'
          }
        }
      };

      const mockAxios = {
        get: jest.fn().mockResolvedValue(mockGetResponse),
        post: jest.fn().mockResolvedValue(mockCreateResponse)
      };

      const originalGet = mobileAPI.getIncidents;
      const originalCreate = mobileAPI.createIncident;
      
      mobileAPI.getIncidents = () => mockAxios.get('/mobile/incidents');
      mobileAPI.createIncident = (data: any) => mockAxios.post('/mobile/incidents', data);

      try {
        // Test get incidents
        const getResponse = await mobileAPI.getIncidents();
        expect(mockAxios.get).toHaveBeenCalledWith('/mobile/incidents');
        expect(getResponse.data).toHaveLength(1);
        expect(getResponse.data[0].severity).toBe('HIGH');

        // Test create incident
        const incidentData = {
          type: 'MAINTENANCE',
          severity: 'MEDIUM',
          description: 'Test incident'
        };
        const createResponse = await mobileAPI.createIncident(incidentData);
        expect(mockAxios.post).toHaveBeenCalledWith('/mobile/incidents', incidentData);
        expect(createResponse.data.success).toBe(true);
        expect(createResponse.data.incident.type).toBe('MAINTENANCE');
        
        console.log('✓ Mobile incident management API calls successful');
      } finally {
        mobileAPI.getIncidents = originalGet;
        mobileAPI.createIncident = originalCreate;
      }
    });
  });

  describe('Mobile Authentication Flow', () => {
    it('should handle authentication with mobile token provider', async () => {
      // Test that mobile authentication works without throwing
      const authAvailable = await isAuthenticationAvailable();
      const tokenInfo = await getCurrentTokenInfo();
      
      expect(typeof authAvailable).toBe('boolean');
      expect(tokenInfo).toHaveProperty('type');
      expect(tokenInfo).toHaveProperty('available');
      
      console.log('✓ Mobile authentication flow works');
      console.log(`  - Auth available: ${authAvailable}`);
      console.log(`  - Token type: ${tokenInfo.type}`);
      console.log(`  - Token available: ${tokenInfo.available}`);
    });

    it('should handle API calls with authentication headers', async () => {
      // Mock axios to capture headers
      const mockAxios = {
        get: jest.fn().mockImplementation((url, config) => {
          // Verify that authentication headers are present
          expect(config?.headers).toBeDefined();
          return Promise.resolve({ data: { success: true } });
        }),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      };

      // Test that API calls include proper authentication
      const originalGet = mobileAPI.getDashboard;
      mobileAPI.getDashboard = () => mockAxios.get('/mobile/dashboard', {
        headers: {
          'Authorization': 'Bearer test-token',
          'X-Token-Type': 'development'
        }
      });

      try {
        await mobileAPI.getDashboard();
        expect(mockAxios.get).toHaveBeenCalled();
        console.log('✓ Mobile API calls include authentication headers');
      } finally {
        mobileAPI.getDashboard = originalGet;
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network Error');
      
      const mockAxios = {
        get: jest.fn().mockRejectedValue(networkError)
      };

      const originalGet = mobileAPI.getDashboard;
      mobileAPI.getDashboard = () => mockAxios.get('/mobile/dashboard');

      try {
        await mobileAPI.getDashboard();
      } catch (error) {
        expect(error).toBe(networkError);
        console.log('✓ Mobile API handles network errors');
      } finally {
        mobileAPI.getDashboard = originalGet;
      }
    });

    it('should handle authentication errors gracefully', async () => {
      const authError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' }
        }
      };
      
      const mockAxios = {
        get: jest.fn().mockRejectedValue(authError)
      };

      const originalGet = mobileAPI.getDashboard;
      mobileAPI.getDashboard = () => mockAxios.get('/mobile/dashboard');

      try {
        await mobileAPI.getDashboard();
      } catch (error: any) {
        expect(error.response.status).toBe(401);
        console.log('✓ Mobile API handles authentication errors');
      } finally {
        mobileAPI.getDashboard = originalGet;
      }
    });

    it('should handle server errors gracefully', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { message: 'Internal Server Error' }
        }
      };
      
      const mockAxios = {
        get: jest.fn().mockRejectedValue(serverError)
      };

      const originalGet = mobileAPI.getDashboard;
      mobileAPI.getDashboard = () => mockAxios.get('/mobile/dashboard');

      try {
        await mobileAPI.getDashboard();
      } catch (error: any) {
        expect(error.response.status).toBe(500);
        console.log('✓ Mobile API handles server errors');
      } finally {
        mobileAPI.getDashboard = originalGet;
      }
    });
  });

  describe('Integration with Mobile Components', () => {
    it('should support patterns used by DashboardScreen', async () => {
      // Test the authentication pattern used by DashboardScreen
      const authAvailable = await isAuthenticationAvailable();
      
      if (!authAvailable) {
        console.log('Mobile auth not available - screen would show error');
      } else {
        console.log('Mobile auth available - screen would load data');
      }
      
      // Should not throw regardless of auth state
      expect(typeof authAvailable).toBe('boolean');
    });

    it('should support patterns used by ReportsScreen', async () => {
      // Test the token info pattern used by ReportsScreen
      const tokenInfo = await getCurrentTokenInfo();
      
      expect(tokenInfo).toHaveProperty('type');
      expect(tokenInfo).toHaveProperty('available');
      
      console.log('✓ Mobile token info pattern works for ReportsScreen');
    });

    it('should handle component error scenarios', async () => {
      // Test error handling patterns used by mobile components
      try {
        const mockError = {
          response: { status: 401, data: { message: 'Unauthorized' } }
        };
        
        const mockGet = jest.fn().mockRejectedValue(mockError);
        const originalGet = mobileAPI.getDashboard;
        mobileAPI.getDashboard = mockGet;
        
        await mobileAPI.getDashboard();
      } catch (error: any) {
        // Mobile components should handle this gracefully
        expect(error.response.status).toBe(401);
        console.log('✓ Mobile component error handling pattern works');
      }
    });
  });

  describe('Mobile-Specific Features', () => {
    it('should handle offline scenarios', async () => {
      // Test that authentication checks work even when offline
      const authAvailable = await isAuthenticationAvailable();
      const tokenInfo = await getCurrentTokenInfo();
      
      // Should not throw even in offline scenarios
      expect(typeof authAvailable).toBe('boolean');
      expect(tokenInfo).toHaveProperty('type');
      
      console.log('✓ Mobile authentication works in offline scenarios');
    });

    it('should handle background/foreground transitions', async () => {
      // Test that token provider works across app state changes
      const tokenInfo1 = await getCurrentTokenInfo();
      
      // Simulate app going to background and coming back
      // (In real implementation, this might involve token refresh)
      
      const tokenInfo2 = await getCurrentTokenInfo();
      
      expect(tokenInfo1.type).toBe(tokenInfo2.type);
      console.log('✓ Mobile authentication persists across app state changes');
    });
  });
});