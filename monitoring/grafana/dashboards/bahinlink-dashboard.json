{"dashboard": {"id": null, "title": "BahinLink Security Management Dashboard", "tags": ["bahinlink", "security", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up{job=\"bahinlink-backend\"}", "legendFormat": "Backend Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "API Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"bahinlink-backend\"}[5m])", "legendFormat": "{{method}} {{route}}"}], "yAxes": [{"label": "Requests per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Error Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"bahinlink-backend\",status=~\"5..\"}[5m])", "legendFormat": "5xx errors"}, {"expr": "rate(http_requests_total{job=\"bahinlink-backend\",status=~\"4..\"}[5m])", "legendFormat": "4xx errors"}], "yAxes": [{"label": "Errors per second", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Database Connections", "type": "graph", "targets": [{"expr": "pg_stat_database_numbackends{datname=\"bahinlink\"}", "legendFormat": "Active connections"}], "yAxes": [{"label": "Connections", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes{job=\"bahinlink-backend\"}", "legendFormat": "Backend Memory"}], "yAxes": [{"label": "Memory (bytes)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Active Agents", "type": "stat", "targets": [{"expr": "bahinlink_active_agents_total", "legendFormat": "Active Agents"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}}, {"id": 8, "title": "Active Shifts", "type": "stat", "targets": [{"expr": "bahinlink_active_shifts_total", "legendFormat": "Active Shifts"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}}, {"id": 9, "title": "Open Incidents", "type": "stat", "targets": [{"expr": "bahinlink_open_incidents_total", "legendFormat": "Open Incidents"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 24}}, {"id": 10, "title": "System Health Score", "type": "gauge", "targets": [{"expr": "bahinlink_system_health_score", "legendFormat": "Health Score"}], "fieldConfig": {"defaults": {"min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 24}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}