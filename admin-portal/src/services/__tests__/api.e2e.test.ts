import { 
  analyticsAPI, 
  usersAPI, 
  adminAPI,
  isAuthenticationAvailable, 
  getCurrentTokenInfo,
  refreshAuthToken,
  clearAuthenticationState
} from '../api';

describe('End-to-End Authentication Flow Tests - Admin Portal', () => {
  beforeEach(() => {
    // Clear any global tokens
    delete (window as any).__CLERK_SESSION_TOKEN;
    delete (window as any).Clerk;
    
    // Clear authentication state
    clearAuthenticationState();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    clearAuthenticationState();
  });

  describe('Development Mode Authentication Flow', () => {
    beforeAll(() => {
      // Set development mode
      process.env.NODE_ENV = 'development';
    });

    it('should authenticate and make API calls in development mode', async () => {
      // Check authentication availability
      const authAvailable = await isAuthenticationAvailable();
      console.log('Auth available in dev mode:', authAvailable);

      // Get token info
      const tokenInfo = await getCurrentTokenInfo();
      console.log('Token info in dev mode:', tokenInfo);

      expect(tokenInfo.type).toBe('development');
      expect(tokenInfo.isValid).toBe(true);
      expect(tokenInfo.token).toContain('dev:');
    });

    it('should make authenticated API calls with development token', async () => {
      // Mock the API response to avoid actual network calls
      const mockResponse = {
        data: {
          success: true,
          data: {
            overview: {
              totalUsers: 10,
              activeAgents: 5,
              activeSites: 3,
              activeShifts: 2
            }
          }
        }
      };

      // Mock axios get method
      const mockGet = jest.fn().mockResolvedValue(mockResponse);
      analyticsAPI.getDashboard = mockGet;

      // Make API call
      const response = await analyticsAPI.getDashboard();

      expect(mockGet).toHaveBeenCalled();
      expect(response.data.success).toBe(true);
      expect(response.data.data.overview.totalUsers).toBe(10);
    });

    it('should handle users API with development authentication', async () => {
      // Mock the API response
      const mockResponse = {
        data: {
          success: true,
          data: [
            { id: '1', username: 'admin', role: 'ADMIN' },
            { id: '2', username: 'user', role: 'USER' }
          ]
        }
      };

      const mockGet = jest.fn().mockResolvedValue(mockResponse);
      usersAPI.getAll = mockGet;

      const response = await usersAPI.getAll();

      expect(mockGet).toHaveBeenCalled();
      expect(response.data.success).toBe(true);
      expect(response.data.data).toHaveLength(2);
    });

    it('should handle admin API with development authentication', async () => {
      // Mock the API response
      const mockResponse = {
        data: {
          success: true,
          data: {
            totalAgents: 15,
            activeShifts: 8
          }
        }
      };

      const mockGet = jest.fn().mockResolvedValue(mockResponse);
      adminAPI.getPerformanceMetrics = mockGet;

      const response = await adminAPI.getPerformanceMetrics();

      expect(mockGet).toHaveBeenCalled();
      expect(response.data.success).toBe(true);
      expect(response.data.data.totalAgents).toBe(15);
    });
  });

  describe('JWT Token Authentication Flow', () => {
    it('should handle JWT token authentication', async () => {
      // Set up a mock JWT token
      const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      
      // Set global Clerk token
      (window as any).__CLERK_SESSION_TOKEN = jwtToken;

      // Check authentication
      const authAvailable = await isAuthenticationAvailable();
      expect(authAvailable).toBe(true);

      // Get token info
      const tokenInfo = await getCurrentTokenInfo();
      expect(tokenInfo.type).toBe('jwt');
      expect(tokenInfo.token).toBe(jwtToken);
      expect(tokenInfo.isValid).toBe(true);
    });

    it('should make API calls with JWT token', async () => {
      const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      (window as any).__CLERK_SESSION_TOKEN = jwtToken;

      // Mock API response
      const mockResponse = {
        data: {
          success: true,
          data: { message: 'Authenticated with JWT' }
        }
      };

      const mockGet = jest.fn().mockResolvedValue(mockResponse);
      analyticsAPI.getDashboard = mockGet;

      const response = await analyticsAPI.getDashboard();

      expect(mockGet).toHaveBeenCalled();
      expect(response.data.success).toBe(true);
    });
  });

  describe('Error Handling and Fallback Mechanisms', () => {
    it('should handle authentication failures gracefully', async () => {
      // Clear all tokens
      delete (window as any).__CLERK_SESSION_TOKEN;
      clearAuthenticationState();

      // Set production mode to test fallback
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      try {
        const authAvailable = await isAuthenticationAvailable();
        console.log('Auth available in production without token:', authAvailable);

        // Should handle gracefully without throwing
        const tokenInfo = await getCurrentTokenInfo();
        expect(tokenInfo.isValid).toBe(false);
      } finally {
        process.env.NODE_ENV = originalEnv;
      }
    });

    it('should handle token refresh scenarios', async () => {
      // Test token refresh with no token
      const refreshResult1 = await refreshAuthToken();
      expect(typeof refreshResult1).toBe('boolean');

      // Test token refresh with development token
      process.env.NODE_ENV = 'development';
      const refreshResult2 = await refreshAuthToken();
      expect(typeof refreshResult2).toBe('boolean');
    });

    it('should handle API errors with proper authentication context', async () => {
      // Mock an API error response
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' }
        }
      };

      const mockGet = jest.fn().mockRejectedValue(mockError);
      analyticsAPI.getDashboard = mockGet;

      try {
        await analyticsAPI.getDashboard();
      } catch (error: any) {
        expect(error.response.status).toBe(401);
        expect(mockGet).toHaveBeenCalled();
      }
    });
  });

  describe('Token Type Detection and Handling', () => {
    it('should properly detect and handle different token types', async () => {
      const testCases = [
        {
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.payload.signature',
          expectedType: 'jwt',
          description: 'JWT token'
        },
        {
          token: 'dev:<EMAIL>',
          expectedType: 'development',
          description: 'Development token'
        },
        {
          token: '<EMAIL>',
          expectedType: 'email',
          description: 'Email token'
        }
      ];

      for (const testCase of testCases) {
        // Set the token globally
        (window as any).__CLERK_SESSION_TOKEN = testCase.token;

        const tokenInfo = await getCurrentTokenInfo();
        
        expect(tokenInfo.type).toBe(testCase.expectedType);
        expect(tokenInfo.token).toBe(testCase.token);
        
        console.log(`✓ ${testCase.description} detected correctly as ${testCase.expectedType}`);
      }
    });
  });

  describe('Circuit Breaker and Retry Logic', () => {
    it('should handle repeated authentication failures', async () => {
      // This test verifies that the system handles repeated auth failures gracefully
      const mockError = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' }
        }
      };

      let callCount = 0;
      const mockGet = jest.fn().mockImplementation(() => {
        callCount++;
        return Promise.reject(mockError);
      });

      analyticsAPI.getDashboard = mockGet;

      // Make multiple failed requests
      for (let i = 0; i < 3; i++) {
        try {
          await analyticsAPI.getDashboard();
        } catch (error) {
          // Expected to fail
        }
      }

      expect(callCount).toBe(3);
      console.log('✓ Circuit breaker logic handled repeated failures');
    });
  });

  describe('Integration with Component Patterns', () => {
    it('should support the isAuthenticationAvailable pattern used by components', async () => {
      // Test the pattern used by components like DashboardPage
      const authAvailable = await isAuthenticationAvailable();
      
      if (!authAvailable) {
        console.log('Authentication not available - component would show error');
        expect(authAvailable).toBe(false);
      } else {
        console.log('Authentication available - component would proceed');
        expect(authAvailable).toBe(true);
      }

      // This should not throw regardless of auth state
      expect(typeof authAvailable).toBe('boolean');
    });

    it('should support the getCurrentTokenInfo pattern used by components', async () => {
      // Test the pattern used by components for debugging
      const tokenInfo = await getCurrentTokenInfo();
      
      expect(tokenInfo).toHaveProperty('type');
      expect(tokenInfo).toHaveProperty('token');
      expect(tokenInfo).toHaveProperty('isValid');
      
      console.log(`✓ Token info pattern works: ${tokenInfo.type} token, valid: ${tokenInfo.isValid}`);
    });
  });
});