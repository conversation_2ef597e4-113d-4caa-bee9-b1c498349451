<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #1565c0;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Connection Test</h1>
        <p>This page tests direct API connectivity to the backend server.</p>
        
        <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <button onclick="testDashboardAPI()">Test Dashboard API</button>
        <button onclick="testUsersAPI()">Test Users API</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testHealthEndpoint() {
            addResult('Testing health endpoint...', 'info');
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                addResult(`✅ Health check successful: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                addResult(`❌ Health check failed: ${error.message}`, 'error');
            }
        }

        async function testDashboardAPI() {
            addResult('Testing dashboard API with admin token...', 'info');
            try {
                const response = await fetch(`${API_BASE}/analytics/dashboard`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer <EMAIL>',
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                addResult(`✅ Dashboard API successful:
Success: ${data.success}
Total Users: ${data.data?.overview?.totalUsers || 'N/A'}
Total Agents: ${data.data?.overview?.totalAgents || 'N/A'}
Active Sites: ${data.data?.overview?.activeSites || 'N/A'}`, 'success');
            } catch (error) {
                addResult(`❌ Dashboard API failed: ${error.message}`, 'error');
            }
        }

        async function testUsersAPI() {
            addResult('Testing users API...', 'info');
            try {
                const response = await fetch(`${API_BASE}/users`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer <EMAIL>',
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                addResult(`✅ Users API successful:
Success: ${data.success}
User Count: ${data.data?.users?.length || 'N/A'}`, 'success');
            } catch (error) {
                addResult(`❌ Users API failed: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            addResult('🚀 Starting API connectivity tests...', 'info');
            setTimeout(testHealthEndpoint, 500);
            setTimeout(testDashboardAPI, 1500);
            setTimeout(testUsersAPI, 2500);
        };
    </script>
</body>
</html>
