name: BahinLink CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  release:
    types: [published]

env:
  NODE_VERSION: '18.x'
  PYTHON_VERSION: '3.9'
  JAVA_VERSION: '11'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run Prettier check
        run: npm run format:check

      - name: Run TypeScript check
        run: npm run type-check

      - name: Security audit
        run: npm audit --audit-level=high

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript, typescript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Backend Tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: bahinlink_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/bahinlink_test
          REDIS_URL: redis://localhost:6379

      - name: Run unit tests
        run: npm run test:backend:unit
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/bahinlink_test
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-jwt-secret
          JWT_REFRESH_SECRET: test-jwt-refresh-secret

      - name: Run integration tests
        run: npm run test:backend:integration
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/bahinlink_test
          REDIS_URL: redis://localhost:6379

      - name: Upload backend coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/backend/lcov.info
          flags: backend
          name: backend-coverage

  # Frontend Tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [mobile-app, admin-portal, client-portal]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ${{ matrix.app }} tests
        run: npm run test:${{ matrix.app }}
        env:
          CI: true

      - name: Upload ${{ matrix.app }} coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/${{ matrix.app }}/lcov.info
          flags: ${{ matrix.app }}
          name: ${{ matrix.app }}-coverage

  # E2E Tests
  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: bahinlink_e2e
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build applications
        run: |
          npm run build:backend
          npm run build:admin-portal
          npm run build:client-portal

      - name: Start test environment
        run: |
          npm run start:test-env &
          sleep 30
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/bahinlink_e2e
          REDIS_URL: redis://localhost:6379

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          BASE_URL: http://localhost:3000

      - name: Upload E2E test artifacts
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: e2e-screenshots
          path: e2e/screenshots/

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [backend-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: npm run test:performance

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results/

  # Security Tests
  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: [code-quality]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run OWASP ZAP scan
        uses: zaproxy/action-full-scan@v0.7.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

      - name: Run Semgrep security scan
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten

  # Build and Deploy
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: [code-quality, backend-tests, frontend-tests]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build applications
        run: |
          npm run build:backend
          npm run build:admin-portal
          npm run build:client-portal
          npm run build:mobile-app

      - name: Build Docker images
        run: |
          docker build -t bahinlink/backend:${{ github.sha }} -f backend/Dockerfile .
          docker build -t bahinlink/admin-portal:${{ github.sha }} -f admin-portal/Dockerfile .
          docker build -t bahinlink/client-portal:${{ github.sha }} -f client-portal/Dockerfile .

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Push Docker images
        run: |
          docker push bahinlink/backend:${{ github.sha }}
          docker push bahinlink/admin-portal:${{ github.sha }}
          docker push bahinlink/client-portal:${{ github.sha }}

      - name: Deploy to staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "Deploying to staging environment"
          # Add staging deployment commands here

      - name: Deploy to production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Deploying to production environment"
          # Add production deployment commands here

  # Mobile App Build
  mobile-build:
    name: Mobile App Build
    runs-on: ubuntu-latest
    needs: [frontend-tests]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Expo CLI
        run: npm install -g @expo/cli

      - name: Install dependencies
        run: npm ci

      - name: Build mobile app
        run: |
          cd mobile-app
          expo build:android --type apk
          expo build:ios --type archive

      - name: Upload mobile artifacts
        uses: actions/upload-artifact@v3
        with:
          name: mobile-builds
          path: |
            mobile-app/builds/
            mobile-app/dist/

  # Notification
  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [build-and-deploy, e2e-tests, security-tests]
    if: always()

    steps:
      - name: Notify Slack on success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: '✅ BahinLink deployment successful!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#deployments'
          text: '❌ BahinLink deployment failed!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
