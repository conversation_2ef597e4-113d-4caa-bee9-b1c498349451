# BahinLink Admin Portal User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [User Management](#user-management)
4. [Agent Management](#agent-management)
5. [Client Management](#client-management)
6. [Site Management](#site-management)
7. [Shift Scheduling](#shift-scheduling)
8. [Incident Management](#incident-management)
9. [Reports & Analytics](#reports--analytics)
10. [System Settings](#system-settings)

## Getting Started

### Logging In
1. Navigate to the admin portal URL
2. Enter your email and password
3. Complete two-factor authentication if enabled
4. You'll be redirected to the dashboard

### First-Time Setup
After logging in for the first time:
1. Complete your profile information
2. Set up notification preferences
3. Configure system settings
4. Add your first client and site

## Dashboard Overview

The dashboard provides a real-time overview of your security operations:

### Key Metrics Cards
- **Total Agents**: Number of active agents in the system
- **Active Shifts**: Currently ongoing shifts
- **Open Incidents**: Unresolved security incidents
- **Completed Reports**: Reports submitted today

### Recent Activity Feed
Shows the latest system activities including:
- New incident reports
- Shift status changes
- Agent check-ins
- System alerts

### Quick Actions
- Create new incident
- Schedule shift
- Add new agent
- Generate report

## User Management

### Adding New Users
1. Navigate to **Users** → **Add User**
2. Fill in required information:
   - Email address
   - First and last name
   - Role (Admin, Manager, Agent, Client)
   - Phone number
3. Set initial password or send invitation email
4. Assign appropriate permissions

### User Roles
- **Admin**: Full system access
- **Manager**: Site and agent management
- **Agent**: Field operations and reporting
- **Client**: View-only access to their sites

### Managing User Permissions
1. Go to **Users** → Select user
2. Click **Edit Permissions**
3. Toggle specific permissions:
   - View reports
   - Manage agents
   - Create incidents
   - Access analytics

## Agent Management

### Agent Onboarding
1. Create user account with Agent role
2. Navigate to **Agents** → **Add Agent**
3. Complete agent profile:
   - Employee ID
   - Hire date
   - Certifications
   - Emergency contacts
   - Training records

### Agent Status Management
- **Active**: Available for shifts
- **Inactive**: Temporarily unavailable
- **On Leave**: Extended absence
- **Terminated**: No longer employed

### Training & Certifications
1. Go to **Agents** → Select agent → **Training**
2. Add certifications:
   - Security license
   - First aid certification
   - Equipment training
3. Set expiration dates and reminders

## Client Management

### Adding New Clients
1. Navigate to **Clients** → **Add Client**
2. Enter company information:
   - Company name
   - Contact details
   - Billing address
   - Contract terms
3. Set up billing preferences
4. Assign account manager

### Client Portal Access
1. Create client user account
2. Assign to specific client
3. Configure portal permissions:
   - View incidents
   - Access reports
   - Site monitoring
   - Communication tools

## Site Management

### Creating Sites
1. Go to **Sites** → **Add Site**
2. Enter site details:
   - Site name and address
   - Client assignment
   - Site type (commercial, residential, industrial)
   - Security level (low, medium, high)
3. Set up site-specific requirements:
   - Number of agents needed
   - Special instructions
   - Emergency contacts

### Site Configuration
- **Geofencing**: Set up virtual boundaries
- **Checkpoints**: Define patrol points
- **Access Control**: Manage entry permissions
- **Equipment**: Track security devices

## Shift Scheduling

### Creating Shifts
1. Navigate to **Scheduling** → **Create Shift**
2. Select:
   - Agent
   - Site
   - Date and time
   - Shift type (regular, overtime, emergency)
3. Add special instructions if needed

### Shift Types
- **Regular**: Standard scheduled shifts
- **Overtime**: Extended hours
- **Emergency**: Urgent coverage needed
- **Training**: Educational sessions

### Bulk Scheduling
1. Go to **Scheduling** → **Bulk Create**
2. Select multiple agents and sites
3. Set recurring schedule pattern
4. Review and confirm assignments

## Incident Management

### Incident Workflow
1. **Reported**: Initial incident creation
2. **Acknowledged**: Supervisor awareness
3. **In Progress**: Active response
4. **Resolved**: Issue addressed
5. **Closed**: Final review complete

### Creating Incidents
1. Click **+ New Incident**
2. Fill in details:
   - Incident type
   - Severity level
   - Location
   - Description
3. Assign to appropriate agent
4. Set priority and urgency

### Incident Types
- **Security Breach**: Unauthorized access
- **Suspicious Activity**: Potential threats
- **Equipment Failure**: Technical issues
- **Medical Emergency**: Health incidents
- **Fire/Safety**: Emergency situations

## Reports & Analytics

### Standard Reports
- **Daily Activity Report**: 24-hour summary
- **Agent Performance**: Individual metrics
- **Site Security Report**: Location-specific data
- **Incident Summary**: Security events analysis

### Custom Reports
1. Go to **Reports** → **Custom Report**
2. Select data sources:
   - Incidents
   - Shifts
   - Agent activity
   - Client metrics
3. Choose date range and filters
4. Generate and export

### Analytics Dashboard
- **Performance Metrics**: KPIs and trends
- **Heat Maps**: Activity visualization
- **Predictive Analytics**: Forecasting
- **Compliance Reports**: Regulatory requirements

## System Settings

### General Settings
- Company information
- Time zone configuration
- Default preferences
- System maintenance windows

### Notification Settings
Configure alerts for:
- Critical incidents
- Shift changes
- System errors
- Performance thresholds

### Integration Settings
- Email service configuration
- SMS provider setup
- Third-party API connections
- Webhook endpoints

### Security Settings
- Password policies
- Two-factor authentication
- Session timeouts
- Access logging

## Best Practices

### Daily Operations
1. Review dashboard metrics each morning
2. Check for overnight incidents
3. Verify shift coverage for the day
4. Address any system alerts

### Weekly Tasks
1. Review agent performance reports
2. Update client communications
3. Analyze incident trends
4. Plan upcoming schedules

### Monthly Activities
1. Generate compliance reports
2. Review system performance
3. Update training records
4. Conduct security audits

## Troubleshooting

### Common Issues
- **Login Problems**: Check credentials and 2FA
- **Slow Performance**: Clear browser cache
- **Missing Data**: Verify permissions
- **Report Errors**: Check date ranges and filters

### Getting Help
- **Help Center**: Built-in documentation
- **Support Ticket**: Technical assistance
- **Training Videos**: Step-by-step guides
- **User Community**: Peer support

## Mobile App Integration

### Agent Mobile App
- Real-time communication with field agents
- GPS tracking and geofencing
- Incident reporting from mobile devices
- Photo and document uploads

### Client Mobile Access
- Mobile-responsive client portal
- Push notifications for incidents
- Real-time site monitoring
- Quick communication tools

## Compliance & Audit

### Audit Trail
All system activities are logged including:
- User logins and actions
- Data modifications
- Report generation
- System configuration changes

### Compliance Reports
- **SOC 2**: Security controls
- **GDPR**: Data protection
- **Industry Standards**: Sector-specific requirements
- **Custom Compliance**: Client-specific needs

## Advanced Features

### API Integration
- RESTful API for third-party connections
- Webhook support for real-time updates
- Custom integrations with client systems
- Data export capabilities

### Automation
- Automated shift scheduling
- Incident escalation rules
- Report generation scheduling
- Alert notifications

### Customization
- Custom fields for clients and sites
- Branded client portals
- Configurable workflows
- Custom report templates

---

For additional support, contact our help <NAME_EMAIL> or call 1-800-BAHINLINK.
