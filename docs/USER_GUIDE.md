# BahinLink User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Admin Portal](#admin-portal)
3. [Client Portal](#client-portal)
4. [Mobile Application](#mobile-application)
5. [Common Tasks](#common-tasks)
6. [Troubleshooting](#troubleshooting)
7. [Support](#support)

## Getting Started

### System Requirements

**For Web Portals:**
- Modern web browser (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- Stable internet connection
- JavaScript enabled

**For Mobile App:**
- iOS 12.0+ or Android 8.0+
- 100MB available storage
- Location services enabled
- Camera access (for report photos)

### First-Time Login

1. **Receive Credentials**: Your system administrator will provide you with:
   - Username
   - Temporary password
   - Portal URL

2. **Initial Login**:
   - Navigate to the provided URL
   - Enter your username and temporary password
   - You'll be prompted to change your password
   - Set up two-factor authentication (if enabled)

3. **Profile Setup**:
   - Complete your profile information
   - Upload a profile photo
   - Set your preferences and notifications

## Admin Portal

The Admin Portal is designed for system administrators and supervisors to manage the entire security workforce operation.

### Dashboard Overview

The main dashboard provides:
- **Key Metrics**: Active agents, ongoing shifts, reports submitted
- **Real-time Map**: Live agent locations and site status
- **Recent Activity**: Latest system events and updates
- **System Health**: Performance indicators and alerts
- **Quick Actions**: Common administrative tasks

### User Management

#### Creating Users

1. Navigate to **Users** → **Add User**
2. Fill in required information:
   - Username (unique identifier)
   - Email address
   - Role (Admin, Supervisor, Agent, Client)
   - Personal information
3. Set initial password or send invitation email
4. Assign appropriate permissions

#### Managing User Roles

**Admin**: Full system access
- User management
- System configuration
- Analytics and reporting
- All operational functions

**Supervisor**: Operational management
- Agent management
- Shift scheduling
- Report review and approval
- Site oversight

**Agent**: Field operations
- Shift management
- Report submission
- Location tracking
- Mobile app access

**Client**: Limited access
- Site monitoring
- Report viewing
- Performance analytics
- Billing information

### Agent Management

#### Agent Profiles

Each agent profile includes:
- **Personal Information**: Contact details, emergency contacts
- **Employment Details**: Employee ID, hire date, employment type
- **Skills & Certifications**: Security skills, training certificates
- **Performance Metrics**: Attendance, report quality, client feedback
- **Scheduling Preferences**: Availability, preferred shifts

#### Skills and Certifications

1. **Adding Skills**:
   - Go to agent profile
   - Click "Edit Skills"
   - Select from predefined skills or add custom ones
   - Set proficiency levels

2. **Managing Certifications**:
   - Upload certification documents
   - Set expiration dates
   - Enable automatic renewal reminders

### Site Management

#### Site Configuration

1. **Basic Information**:
   - Site name and address
   - Client assignment
   - Contact information
   - Operating hours

2. **Security Requirements**:
   - Required skills and certifications
   - Equipment needs
   - Special instructions
   - Emergency procedures

3. **Geofencing**:
   - Set site boundaries on map
   - Configure check-in/check-out zones
   - Set up restricted areas

### Shift Scheduling

#### Creating Shifts

1. **Manual Scheduling**:
   - Select site and time period
   - Choose agent or leave for auto-assignment
   - Set shift requirements
   - Add special instructions

2. **Auto-Scheduling**:
   - Define scheduling parameters
   - Set optimization goals (cost, coverage, satisfaction)
   - Review and approve generated schedule
   - Handle conflicts and exceptions

#### Shift Management

- **Shift Status Tracking**: Monitor real-time shift progress
- **Attendance Monitoring**: Track clock-in/clock-out times
- **Location Verification**: Ensure agents are at assigned locations
- **Shift Modifications**: Handle schedule changes and emergencies

### Report Management

#### Report Review Process

1. **Incoming Reports**: Review submitted reports in queue
2. **Quality Check**: Verify completeness and accuracy
3. **Approval/Rejection**: Approve reports or request changes
4. **Client Notification**: Forward approved reports to clients
5. **Archive**: Store completed reports for future reference

#### Report Templates

- **Standard Templates**: Patrol, incident, maintenance reports
- **Custom Templates**: Create site-specific report formats
- **Field Configuration**: Add/remove fields as needed
- **Validation Rules**: Set required fields and data formats

### Analytics and Reporting

#### Operational Analytics

- **Performance Metrics**: Completion rates, response times
- **Cost Analysis**: Labor costs, overtime tracking
- **Quality Indicators**: Report quality, client satisfaction
- **Trend Analysis**: Historical performance patterns

#### Business Intelligence

- **Executive Dashboard**: High-level KPIs and insights
- **Predictive Analytics**: Demand forecasting, resource planning
- **Benchmarking**: Industry comparisons and best practices
- **Custom Reports**: Build reports for specific needs

## Client Portal

The Client Portal provides clients with real-time visibility into their security operations.

### Client Dashboard

#### Real-time Monitoring

- **Site Status**: Current security coverage at each location
- **Agent Tracking**: Live locations of assigned security personnel
- **Incident Alerts**: Immediate notification of security events
- **System Health**: Overall operational status

#### Performance Metrics

- **Service Levels**: Response times, coverage statistics
- **Quality Indicators**: Report completeness, agent performance
- **Cost Tracking**: Service costs and budget utilization
- **Trend Analysis**: Historical performance patterns

### Site Monitoring

#### Live Site View

- **Interactive Map**: Real-time agent positions and movements
- **Site Status**: Active/inactive status for each location
- **Recent Activity**: Latest reports and incidents
- **Alert History**: Past security events and responses

#### Site Details

- **Coverage Schedule**: Planned security coverage times
- **Agent Information**: Assigned personnel and their qualifications
- **Recent Reports**: Latest patrol and incident reports
- **Performance Data**: Site-specific metrics and trends

### Report Access

#### Report Library

- **Search and Filter**: Find reports by date, type, site, or keyword
- **Report Viewer**: Read detailed reports with photos and attachments
- **Download Options**: Export reports in PDF or Excel format
- **Approval Workflow**: Review and approve reports requiring client sign-off

#### Report Types

- **Patrol Reports**: Regular security rounds and observations
- **Incident Reports**: Security events and emergency responses
- **Maintenance Reports**: Equipment and facility issues
- **Special Reports**: Custom reports for specific requirements

### Communication

#### Messaging System

- **Direct Messages**: Communicate with security supervisors
- **Broadcast Messages**: Send announcements to all agents
- **Emergency Alerts**: Immediate notification system
- **Message History**: Archive of all communications

## Mobile Application

The mobile app is designed for security agents to manage their duties while in the field.

### Getting Started

#### App Installation

1. **Download**: Get the app from App Store (iOS) or Google Play (Android)
2. **Login**: Use your provided credentials
3. **Permissions**: Grant location, camera, and notification access
4. **Setup**: Complete profile and preferences

#### Initial Configuration

- **Profile Setup**: Verify personal information
- **Notification Settings**: Choose alert preferences
- **Offline Mode**: Enable for areas with poor connectivity
- **Security Settings**: Set up app lock (PIN/biometric)

### Shift Management

#### Viewing Schedules

- **My Shifts**: View upcoming and past assignments
- **Shift Details**: Location, time, special instructions
- **Calendar View**: Monthly and weekly schedule overview
- **Notifications**: Reminders for upcoming shifts

#### Clock In/Out

1. **Arrival at Site**:
   - Open app and select current shift
   - Verify location (GPS check)
   - Take arrival photo (if required)
   - Clock in with timestamp

2. **Departure from Site**:
   - Complete end-of-shift checklist
   - Take departure photo (if required)
   - Clock out with timestamp
   - Submit any pending reports

### Location Tracking

#### GPS Monitoring

- **Automatic Tracking**: Continuous location updates during shifts
- **Geofence Compliance**: Alerts when leaving assigned areas
- **Battery Optimization**: Efficient tracking to preserve battery life
- **Offline Capability**: Store location data when connectivity is poor

#### Privacy Controls

- **Shift-Only Tracking**: Location tracking only during work hours
- **Data Security**: Encrypted transmission and storage
- **Transparency**: Clear indication when tracking is active
- **Opt-out Options**: Disable tracking when off-duty

### Report Submission

#### Creating Reports

1. **Select Report Type**: Patrol, incident, maintenance, or custom
2. **Fill Required Fields**: Complete all mandatory information
3. **Add Photos**: Capture relevant images with descriptions
4. **Record Audio**: Add voice notes for detailed explanations
5. **Review and Submit**: Check accuracy before submission

#### Report Types

**Patrol Reports**:
- Areas covered during patrol
- Observations and findings
- Security checks performed
- Any issues discovered

**Incident Reports**:
- Detailed incident description
- Time and location
- People involved
- Actions taken
- Follow-up required

**Maintenance Reports**:
- Equipment issues
- Facility problems
- Safety hazards
- Repair recommendations

#### Offline Capability

- **Draft Storage**: Save reports when offline
- **Auto-Sync**: Upload when connection restored
- **Photo Compression**: Optimize images for faster upload
- **Queue Management**: Handle multiple pending reports

### Emergency Features

#### Panic Button

- **Quick Access**: Prominent emergency button on main screen
- **Instant Alert**: Immediate notification to supervisors
- **Location Sharing**: Automatic GPS coordinates transmission
- **Audio Recording**: Capture ambient sound for context

#### Emergency Contacts

- **Quick Dial**: One-touch calling to emergency services
- **Supervisor Contact**: Direct line to security supervisor
- **Client Contact**: Emergency contact for site issues
- **Medical Emergency**: Direct connection to medical services

## Common Tasks

### For Administrators

#### Setting Up a New Client

1. **Create Client Account**:
   - Navigate to Clients → Add Client
   - Enter company information
   - Set up billing details
   - Configure service parameters

2. **Add Client Sites**:
   - Create site profiles
   - Set security requirements
   - Configure geofencing
   - Upload site maps/photos

3. **Assign Personnel**:
   - Select qualified agents
   - Create shift schedules
   - Set up reporting requirements
   - Configure client access

#### Managing Schedules

1. **Weekly Schedule Planning**:
   - Review upcoming week
   - Check agent availability
   - Identify coverage gaps
   - Optimize assignments

2. **Handling Schedule Changes**:
   - Process time-off requests
   - Manage sick leave coverage
   - Handle emergency assignments
   - Communicate changes to all parties

### For Supervisors

#### Daily Operations

1. **Morning Briefing**:
   - Review overnight reports
   - Check current shift status
   - Address any issues
   - Plan day's activities

2. **Ongoing Monitoring**:
   - Track agent locations
   - Monitor report submissions
   - Handle client communications
   - Address emergencies

#### Report Review

1. **Quality Control**:
   - Check report completeness
   - Verify accuracy of information
   - Ensure proper documentation
   - Provide feedback to agents

2. **Client Communication**:
   - Forward approved reports
   - Explain any incidents
   - Address client concerns
   - Maintain service standards

### For Agents

#### Starting a Shift

1. **Pre-Shift Preparation**:
   - Review shift details
   - Check equipment
   - Plan patrol routes
   - Note special instructions

2. **Arrival Procedures**:
   - Clock in via mobile app
   - Verify location
   - Conduct initial site check
   - Report any immediate issues

#### During the Shift

1. **Regular Patrols**:
   - Follow assigned routes
   - Document observations
   - Check security points
   - Maintain visibility

2. **Incident Response**:
   - Assess situation
   - Take appropriate action
   - Document thoroughly
   - Report immediately

### For Clients

#### Monitoring Operations

1. **Daily Check**:
   - Review dashboard metrics
   - Check site status
   - Read latest reports
   - Address any concerns

2. **Performance Review**:
   - Analyze trends
   - Compare to SLAs
   - Provide feedback
   - Plan improvements

## Troubleshooting

### Common Issues

#### Login Problems

**Issue**: Cannot log in to the system
**Solutions**:
- Verify username and password
- Check caps lock and typing
- Clear browser cache and cookies
- Try different browser
- Contact administrator for password reset

#### Mobile App Issues

**Issue**: App not tracking location
**Solutions**:
- Check location permissions
- Ensure GPS is enabled
- Restart the app
- Check internet connection
- Update app to latest version

**Issue**: Reports not uploading
**Solutions**:
- Check internet connection
- Verify app permissions
- Clear app cache
- Restart device
- Contact technical support

#### Performance Issues

**Issue**: Slow system response
**Solutions**:
- Check internet speed
- Close unnecessary browser tabs
- Clear browser cache
- Restart browser
- Try different device

### Error Messages

#### "Access Denied"
- Check user permissions
- Verify role assignments
- Contact administrator
- Clear browser session

#### "Connection Timeout"
- Check internet connection
- Try refreshing page
- Wait and retry
- Contact technical support

#### "Invalid Data"
- Check required fields
- Verify data format
- Remove special characters
- Contact support if persistent

### Getting Help

#### Self-Service Options

1. **Help Documentation**: Comprehensive guides and FAQs
2. **Video Tutorials**: Step-by-step visual instructions
3. **Knowledge Base**: Searchable articles and solutions
4. **System Status**: Check for known issues and maintenance

#### Contact Support

1. **In-App Help**: Built-in support chat and ticketing
2. **Email Support**: <EMAIL>
3. **Phone Support**: 1-800-BAHINLINK
4. **Emergency Line**: 24/7 critical issue support

## Support

### Training Resources

#### Online Training

- **Getting Started Course**: Basic system navigation
- **Role-Specific Training**: Customized for each user type
- **Advanced Features**: In-depth functionality training
- **Best Practices**: Industry standards and recommendations

#### Live Training

- **Webinars**: Regular training sessions
- **On-Site Training**: Customized training at your location
- **One-on-One Sessions**: Personalized training support
- **Train-the-Trainer**: Prepare internal trainers

### Documentation

- **User Guides**: Detailed instructions for all features
- **API Documentation**: Technical integration guides
- **Video Library**: Visual tutorials and demonstrations
- **Release Notes**: Updates and new feature announcements

### Community

- **User Forum**: Connect with other BahinLink users
- **Best Practices**: Share tips and experiences
- **Feature Requests**: Suggest improvements
- **Beta Testing**: Try new features early

### Contact Information

**General Support**:
- Email: <EMAIL>
- Phone: 1-800-BAHINLINK
- Hours: Monday-Friday, 8 AM - 6 PM EST

**Emergency Support**:
- Phone: 1-800-EMERGENCY
- Available: 24/7/365

**Sales and Business**:
- Email: <EMAIL>
- Phone: 1-800-SALES
- Hours: Monday-Friday, 9 AM - 5 PM EST

**Technical Integration**:
- Email: <EMAIL>
- Documentation: https://docs.bahinlink.com
- API Support: Monday-Friday, 9 AM - 5 PM EST

---

*This user guide is regularly updated. For the latest version, visit https://docs.bahinlink.com*
