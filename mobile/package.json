{"name": "bahinlink-mobile", "version": "1.0.0", "description": "BahinLink Mobile Application for Security Workforce Management", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^9.4.1", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.5.0", "expo": "~49.0.10", "expo-av": "~13.4.1", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-file-system": "~15.4.4", "expo-font": "~11.4.0", "expo-image-picker": "~14.3.2", "expo-location": "~16.1.0", "expo-media-library": "~15.4.1", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-task-manager": "~11.3.0", "react": "18.2.0", "react-native": "0.72.4", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "react-native-paper": "^5.10.4", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-signature-canvas": "^4.7.2", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.0.0", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.6.3", "typescript": "^5.1.3"}, "keywords": ["react-native", "expo", "security", "workforce-management", "mobile"], "author": "BahinLink Development Team", "license": "MIT", "private": true}