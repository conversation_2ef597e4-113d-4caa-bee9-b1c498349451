apiVersion: apps/v1
kind: Deployment
metadata:
  name: bahinlink-backend
  namespace: bahinlink-production
  labels:
    app: bahinlink-backend
    component: api
    environment: production
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: bahinlink-backend
  template:
    metadata:
      labels:
        app: bahinlink-backend
        component: api
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: bahinlink-backend-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: backend
        image: bahinlink/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        - containerPort: 3001
          name: websocket
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: WS_PORT
          value: "3001"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: jwt-secret
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: jwt-refresh-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: encryption-key
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access-key-id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret-access-key
        - name: AWS_REGION
          valueFrom:
            configMapKeyRef:
              name: bahinlink-config
              key: aws-region
        - name: S3_BUCKET
          valueFrom:
            configMapKeyRef:
              name: bahinlink-config
              key: s3-bucket
        - name: FIREBASE_PROJECT_ID
          valueFrom:
            configMapKeyRef:
              name: bahinlink-config
              key: firebase-project-id
        - name: FIREBASE_CLIENT_EMAIL
          valueFrom:
            secretKeyRef:
              name: firebase-credentials
              key: client-email
        - name: FIREBASE_PRIVATE_KEY
          valueFrom:
            secretKeyRef:
              name: firebase-credentials
              key: private-key
        - name: SMTP_HOST
          valueFrom:
            configMapKeyRef:
              name: bahinlink-config
              key: smtp-host
        - name: SMTP_PORT
          valueFrom:
            configMapKeyRef:
              name: bahinlink-config
              key: smtp-port
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: smtp-credentials
              key: username
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: smtp-credentials
              key: password
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: logs
          mountPath: /app/logs
        - name: temp
          mountPath: /tmp
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: bahinlink-uploads-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: bahinlink-logs-pvc
      - name: temp
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - bahinlink-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: bahinlink-backend-service
  namespace: bahinlink-production
  labels:
    app: bahinlink-backend
    component: api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: websocket
  selector:
    app: bahinlink-backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: bahinlink-backend-sa
  namespace: bahinlink-production
  labels:
    app: bahinlink-backend
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: bahinlink-production
  name: bahinlink-backend-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: bahinlink-backend-rolebinding
  namespace: bahinlink-production
subjects:
- kind: ServiceAccount
  name: bahinlink-backend-sa
  namespace: bahinlink-production
roleRef:
  kind: Role
  name: bahinlink-backend-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: bahinlink-backend-pdb
  namespace: bahinlink-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: bahinlink-backend
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bahinlink-backend-hpa
  namespace: bahinlink-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bahinlink-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
