apiVersion: v1
kind: Namespace
metadata:
  name: bahinlink-production
  labels:
    name: bahinlink-production
    environment: production
    app: bahinlink
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: bahinlink-resource-quota
  namespace: bahinlink-production
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: bahinlink-limit-range
  namespace: bahinlink-production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
