# This file contains template secrets - replace with actual values before deployment
apiVersion: v1
kind: Secret
metadata:
  name: bahinlink-secrets
  namespace: bahinlink
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  database-url: ************************************************************************************************
  redis-url: cmVkaXM6Ly86cmVkaXNfcGFzc3dvcmRAcmVkaXMtc2VydmljZTo2Mzc5
  redis-password: cmVkaXNfcGFzc3dvcmQ=
  jwt-secret: eW91cl9zdXBlcl9zZWN1cmVfand0X3NlY3JldF9rZXlfYXRfbGVhc3RfMzJfY2hhcmFjdGVyc19sb25n
  clerk-secret-key: c2tfdGVzdF95b3VyX2NsZXJrX3NlY3JldF9rZXk=
  clerk-publishable-key: cGtfdGVzdF95b3VyX2NsZXJrX3B1Ymxpc2hhYmxlX2tleQ==
  grafana-password: Z3JhZmFuYV9hZG1pbl9wYXNzd29yZA==
  sendgrid-api-key: U0cueW91cl9zZW5kZ3JpZF9hcGlfa2V5
  twilio-account-sid: QUN5b3VyX3R3aWxpb19hY2NvdW50X3NpZA==
  twilio-auth-token: eW91cl90d2lsaW9fYXV0aF90b2tlbg==
  aws-access-key-id: eW91cl9hd3NfYWNjZXNzX2tleV9pZA==
  aws-secret-access-key: eW91cl9hd3Nfc2VjcmV0X2FjY2Vzc19rZXk=
  google-maps-api-key: eW91cl9nb29nbGVfbWFwc19hcGlfa2V5
  openweather-api-key: eW91cl9vcGVud2VhdGhlcl9hcGlfa2V5
---
apiVersion: v1
kind: Secret
metadata:
  name: bahinlink-registry-secret
  namespace: bahinlink
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: bahinlink-config
  namespace: bahinlink
data:
  NODE_ENV: "production"
  API_URL: "https://api.bahinlink.com/api"
  REACT_APP_API_URL: "https://api.bahinlink.com/api"
  CORS_ORIGIN: "https://admin.bahinlink.com,https://client.bahinlink.com"
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  HELMET_ENABLED: "true"
  CSRF_PROTECTION: "true"
  ENABLE_ANALYTICS: "true"
  ENABLE_NOTIFICATIONS: "true"
  ENABLE_GEOFENCING: "true"
  ENABLE_WEATHER_INTEGRATION: "true"
  LOG_LEVEL: "info"
  BACKUP_ENABLED: "true"
  BACKUP_RETENTION_DAYS: "30"
  HEALTH_CHECK_TIMEOUT: "300"
  ROLLBACK_ON_FAILURE: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: bahinlink
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;
        
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/javascript
            application/xml+rss
            application/json
            application/xml
            image/svg+xml;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        include /etc/nginx/conf.d/*.conf;
    }
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: bahinlink-uploads-pvc
  namespace: bahinlink
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: efs-storage
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: bahinlink-backup
  namespace: bahinlink
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: bahinlink/backup:latest
            env:
            - name: POSTGRES_HOST
              value: "postgres-service"
            - name: POSTGRES_DB
              value: "bahinlink"
            - name: POSTGRES_USER
              value: "postgres"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_PASSWORD
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: bahinlink-secrets
                  key: aws-access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: bahinlink-secrets
                  key: aws-secret-access-key
            - name: AWS_S3_BACKUP_BUCKET
              value: "bahinlink-backups"
            - name: RETENTION_DAYS
              value: "30"
            volumeMounts:
            - name: backup-storage
              mountPath: /backups
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-pvc
  namespace: bahinlink
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: standard
