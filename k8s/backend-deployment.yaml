apiVersion: apps/v1
kind: Deployment
metadata:
  name: bahinlink-backend
  namespace: bahinlink
  labels:
    app: bahinlink-backend
    tier: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: bahinlink-backend
  template:
    metadata:
      labels:
        app: bahinlink-backend
        tier: backend
    spec:
      containers:
      - name: backend
        image: bahinlink/backend:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: jwt-secret
        - name: CLERK_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: bahinlink-secrets
              key: clerk-secret-key
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: bahinlink-uploads-pvc
      imagePullSecrets:
      - name: bahinlink-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: bahinlink-backend-service
  namespace: bahinlink
  labels:
    app: bahinlink-backend
spec:
  selector:
    app: bahinlink-backend
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: bahinlink-backend-hpa
  namespace: bahinlink
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: bahinlink-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
